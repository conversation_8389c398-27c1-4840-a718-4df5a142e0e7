"use client"

import { useEffect, useRef } from "react"
import { useScroll, useTransform, useSpring } from "framer-motion"

export function useScrollAnimations() {
  const { scrollY } = useScroll()
  
  // Parallax transforms for different speeds
  const parallaxSlow = useTransform(scrollY, [0, 1000], [0, -100])
  const parallaxMedium = useTransform(scrollY, [0, 1000], [0, -200])
  const parallaxFast = useTransform(scrollY, [0, 1000], [0, -300])
  
  // Smooth spring animations
  const smoothParallaxSlow = useSpring(parallaxSlow, { stiffness: 100, damping: 30 })
  const smoothParallaxMedium = useSpring(parallaxMedium, { stiffness: 100, damping: 30 })
  const smoothParallaxFast = useSpring(parallaxFast, { stiffness: 100, damping: 30 })
  
  return {
    parallaxSlow: smoothParallaxSlow,
    parallaxMedium: smoothParallaxMedium,
    parallaxFast: smoothParallaxFast,
    scrollY
  }
}

export function useScrollReveal() {
  const ref = useRef<HTMLElement>(null)
  
  useEffect(() => {
    const element = ref.current
    if (!element) return
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('revealed')
          }
        })
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    )
    
    // Find all scroll-reveal elements within this component
    const scrollElements = element.querySelectorAll('.scroll-reveal')
    scrollElements.forEach((el) => observer.observe(el))
    
    return () => {
      scrollElements.forEach((el) => observer.unobserve(el))
    }
  }, [])
  
  return ref
}

// Utility for staggered animations
export const staggeredRevealVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

export const revealItemVariants = {
  hidden: { 
    opacity: 0, 
    y: 30,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.4, 0, 0.2, 1]
    }
  }
}

// Enhanced hover variants
export const enhancedHoverVariants = {
  initial: { scale: 1, y: 0 },
  hover: { 
    scale: 1.02, 
    y: -4,
    transition: {
      duration: 0.2,
      ease: "easeOut"
    }
  },
  tap: { 
    scale: 0.98,
    transition: {
      duration: 0.1
    }
  }
}

// Floating animation variants
export const floatingVariants = {
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
}
