"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"
import { useScrollAnimations } from "@/hooks/use-scroll-animations"

interface GlobalOrb {
  id: number
  size: number
  x: number
  y: number
  color: string
  animationClass: string
  opacity: number
  delay: number
  parallaxSpeed: 'slow' | 'medium' | 'fast'
  shape: 'circle' | 'ellipse' | 'blob'
  sizeCategory: 'small' | 'medium' | 'large' | 'extra-large'
}

interface GlobalFloatingOrbsProps {
  count?: number
}

export function GlobalFloatingOrbs({ count = 20 }: GlobalFloatingOrbsProps) {
  const { parallaxSlow, parallaxMedium, parallaxFast } = useScrollAnimations()
  const [orbs, setOrbs] = useState<GlobalOrb[]>([])

  useEffect(() => {
    const colors = [
      'rgba(107, 33, 168, 0.15)',   // deep-purple
      'rgba(67, 56, 202, 0.15)',    // indigo
      'rgba(124, 58, 237, 0.15)',   // royal-purple
      'rgba(107, 33, 168, 0.08)',   // deep-purple lighter
      'rgba(67, 56, 202, 0.08)',    // indigo lighter
      'rgba(124, 58, 237, 0.08)',   // royal-purple lighter
    ]

    const animationClasses = [
      'floating-orb-1',
      'floating-orb-2', 
      'floating-orb-3',
      'floating-orb-4',
      'floating-orb-5'
    ]

    const parallaxSpeeds: ('slow' | 'medium' | 'fast')[] = ['slow', 'medium', 'fast']
    const shapes: ('circle' | 'ellipse' | 'blob')[] = ['circle', 'ellipse', 'blob']
    const sizeCategories: ('small' | 'medium' | 'large' | 'extra-large')[] = ['small', 'medium', 'large', 'extra-large']

    const getSizeFromCategory = (category: 'small' | 'medium' | 'large' | 'extra-large') => {
      switch (category) {
        case 'small': return Math.random() * 60 + 30 // 30-90px
        case 'medium': return Math.random() * 100 + 80 // 80-180px
        case 'large': return Math.random() * 150 + 150 // 150-300px
        case 'extra-large': return Math.random() * 200 + 250 // 250-450px
        default: return Math.random() * 100 + 80
      }
    }

    const generateOrbs = () => {
      const newOrbs: GlobalOrb[] = []
      
      for (let i = 0; i < count; i++) {
        const sizeCategory = sizeCategories[Math.floor(Math.random() * sizeCategories.length)]
        newOrbs.push({
          id: i,
          size: getSizeFromCategory(sizeCategory),
          x: Math.random() * 120 - 10, // -10% to 110% for edge overflow
          y: Math.random() * 500, // Spread across entire page height
          color: colors[Math.floor(Math.random() * colors.length)],
          animationClass: animationClasses[Math.floor(Math.random() * animationClasses.length)],
          opacity: Math.random() * 0.25 + 0.05, // 0.05 to 0.3
          delay: Math.random() * 10, // 0 to 10 seconds delay
          parallaxSpeed: parallaxSpeeds[Math.floor(Math.random() * parallaxSpeeds.length)],
          shape: shapes[Math.floor(Math.random() * shapes.length)],
          sizeCategory,
        })
      }
      
      setOrbs(newOrbs)
    }

    generateOrbs()
  }, [count])

  const getParallaxTransform = (speed: 'slow' | 'medium' | 'fast') => {
    switch (speed) {
      case 'slow': return parallaxSlow
      case 'medium': return parallaxMedium
      case 'fast': return parallaxFast
      default: return parallaxSlow
    }
  }

  const getShapeStyles = (shape: 'circle' | 'ellipse' | 'blob', size: number) => {
    const baseStyles = {
      width: `${size}px`,
      height: `${size}px`,
    }

    switch (shape) {
      case 'circle':
        return { ...baseStyles, borderRadius: '50%' }
      case 'ellipse':
        return { 
          ...baseStyles, 
          width: `${size * 1.4}px`,
          height: `${size * 0.7}px`,
          borderRadius: '50%' 
        }
      case 'blob':
        return { 
          ...baseStyles, 
          borderRadius: `${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}%` 
        }
      default:
        return { ...baseStyles, borderRadius: '50%' }
    }
  }

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {orbs.map((orb) => (
        <motion.div
          key={orb.id}
          className={`floating-orb ${orb.animationClass}`}
          style={{
            ...getShapeStyles(orb.shape, orb.size),
            left: `${orb.x}%`,
            top: `${orb.y}vh`,
            backgroundColor: orb.color,
            opacity: orb.opacity,
            animationDelay: `${orb.delay}s`,
            transform: `translate(-50%, -50%)`,
            y: getParallaxTransform(orb.parallaxSpeed),
          }}
        />
      ))}
      
      {/* Additional gradient overlays for depth */}
      <motion.div 
        className="absolute inset-0 bg-gradient-to-br from-transparent via-white/2 to-transparent pointer-events-none"
        style={{ y: parallaxSlow }}
      />
      <motion.div 
        className="absolute inset-0 bg-gradient-to-tr from-purple-50/10 via-transparent to-indigo-50/10 pointer-events-none"
        style={{ y: parallaxMedium }}
      />
    </div>
  )
}
