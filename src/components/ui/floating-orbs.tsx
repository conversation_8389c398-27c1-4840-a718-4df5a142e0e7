"use client"

import { useEffect, useState } from "react"

interface Orb {
  id: number
  size: number
  x: number
  y: number
  color: string
  animationClass: string
  opacity: number
  delay: number
}

interface FloatingOrbsProps {
  count?: number
  className?: string
}

export function FloatingOrbs({ count = 8, className = "" }: FloatingOrbsProps) {
  const [orbs, setOrbs] = useState<Orb[]>([])

  useEffect(() => {
    const colors = [
      'rgba(107, 33, 168, 0.3)',   // deep-purple
      'rgba(67, 56, 202, 0.3)',    // indigo
      'rgba(124, 58, 237, 0.3)',   // royal-purple
      'rgba(107, 33, 168, 0.2)',   // deep-purple lighter
      'rgba(67, 56, 202, 0.2)',    // indigo lighter
    ]

    const animationClasses = [
      'floating-orb-1',
      'floating-orb-2', 
      'floating-orb-3',
      'floating-orb-4',
      'floating-orb-5'
    ]

    const generateOrbs = () => {
      const newOrbs: Orb[] = []
      
      for (let i = 0; i < count; i++) {
        newOrbs.push({
          id: i,
          size: Math.random() * 300 + 100, // 100px to 400px
          x: Math.random() * 100, // 0% to 100%
          y: Math.random() * 100, // 0% to 100%
          color: colors[Math.floor(Math.random() * colors.length)],
          animationClass: animationClasses[Math.floor(Math.random() * animationClasses.length)],
          opacity: Math.random() * 0.4 + 0.1, // 0.1 to 0.5
          delay: Math.random() * 5, // 0 to 5 seconds delay
        })
      }
      
      setOrbs(newOrbs)
    }

    generateOrbs()
  }, [count])

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {orbs.map((orb) => (
        <div
          key={orb.id}
          className={`floating-orb ${orb.animationClass}`}
          style={{
            width: `${orb.size}px`,
            height: `${orb.size}px`,
            left: `${orb.x}%`,
            top: `${orb.y}%`,
            backgroundColor: orb.color,
            opacity: orb.opacity,
            animationDelay: `${orb.delay}s`,
            transform: `translate(-50%, -50%)`,
          }}
        />
      ))}
      
      {/* Additional gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-transparent pointer-events-none" />
    </div>
  )
}
