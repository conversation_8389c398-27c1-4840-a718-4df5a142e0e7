"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"
import { useScrollAnimations } from "@/hooks/use-scroll-animations"

interface Orb {
  id: number
  size: number
  x: number
  y: number
  color: string
  animationClass: string
  opacity: number
  delay: number
  parallaxSpeed: 'slow' | 'medium' | 'fast'
  shape: 'circle' | 'ellipse' | 'blob'
  sizeCategory: 'small' | 'medium' | 'large' | 'extra-large'
}

interface FloatingOrbsProps {
  count?: number
  className?: string
  enableShapes?: boolean
}

export function FloatingOrbs({ count = 8, className = "", enableShapes = true }: FloatingOrbsProps) {
  const { parallaxSlow, parallaxMedium, parallaxFast } = useScrollAnimations()
  const [orbs, setOrbs] = useState<Orb[]>([])

  useEffect(() => {
    const colors = [
      'rgba(107, 33, 168, 0.25)',   // deep-purple
      'rgba(67, 56, 202, 0.25)',    // indigo
      'rgba(124, 58, 237, 0.25)',   // royal-purple
      'rgba(107, 33, 168, 0.15)',   // deep-purple lighter
      'rgba(67, 56, 202, 0.15)',    // indigo lighter
      'rgba(124, 58, 237, 0.15)',   // royal-purple lighter
    ]

    const animationClasses = [
      'floating-orb-1',
      'floating-orb-2',
      'floating-orb-3',
      'floating-orb-4',
      'floating-orb-5'
    ]

    const parallaxSpeeds: ('slow' | 'medium' | 'fast')[] = ['slow', 'medium', 'fast']
    const shapes: ('circle' | 'ellipse' | 'blob')[] = ['circle', 'ellipse', 'blob']
    const sizeCategories: ('small' | 'medium' | 'large' | 'extra-large')[] = ['small', 'medium', 'large', 'extra-large']

    const getSizeFromCategory = (category: 'small' | 'medium' | 'large' | 'extra-large') => {
      switch (category) {
        case 'small': return Math.random() * 80 + 40 // 40-120px
        case 'medium': return Math.random() * 120 + 100 // 100-220px
        case 'large': return Math.random() * 180 + 200 // 200-380px
        case 'extra-large': return Math.random() * 250 + 300 // 300-550px
        default: return Math.random() * 120 + 100
      }
    }

    const generateOrbs = () => {
      const newOrbs: Orb[] = []

      for (let i = 0; i < count; i++) {
        const sizeCategory = sizeCategories[Math.floor(Math.random() * sizeCategories.length)]
        newOrbs.push({
          id: i,
          size: getSizeFromCategory(sizeCategory),
          x: Math.random() * 120 - 10, // -10% to 110% for edge overflow
          y: Math.random() * 120 - 10, // -10% to 110% for edge overflow
          color: colors[Math.floor(Math.random() * colors.length)],
          animationClass: animationClasses[Math.floor(Math.random() * animationClasses.length)],
          opacity: Math.random() * 0.3 + 0.1, // 0.1 to 0.4
          delay: Math.random() * 8, // 0 to 8 seconds delay
          parallaxSpeed: parallaxSpeeds[Math.floor(Math.random() * parallaxSpeeds.length)],
          shape: enableShapes ? shapes[Math.floor(Math.random() * shapes.length)] : 'circle',
          sizeCategory,
        })
      }

      setOrbs(newOrbs)
    }

    generateOrbs()
  }, [count])

  const getParallaxTransform = (speed: 'slow' | 'medium' | 'fast') => {
    switch (speed) {
      case 'slow': return parallaxSlow
      case 'medium': return parallaxMedium
      case 'fast': return parallaxFast
      default: return parallaxSlow
    }
  }

  const getShapeStyles = (shape: 'circle' | 'ellipse' | 'blob', size: number) => {
    const baseStyles = {
      width: `${size}px`,
      height: `${size}px`,
    }

    switch (shape) {
      case 'circle':
        return { ...baseStyles, borderRadius: '50%' }
      case 'ellipse':
        return {
          ...baseStyles,
          width: `${size * 1.4}px`,
          height: `${size * 0.7}px`,
          borderRadius: '50%'
        }
      case 'blob':
        return {
          ...baseStyles,
          borderRadius: `${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}%`
        }
      default:
        return { ...baseStyles, borderRadius: '50%' }
    }
  }

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {orbs.map((orb) => (
        <motion.div
          key={orb.id}
          className={`floating-orb ${orb.animationClass}`}
          style={{
            ...getShapeStyles(orb.shape, orb.size),
            left: `${orb.x}%`,
            top: `${orb.y}%`,
            backgroundColor: orb.color,
            opacity: orb.opacity,
            animationDelay: `${orb.delay}s`,
            transform: `translate(-50%, -50%)`,
            y: getParallaxTransform(orb.parallaxSpeed),
          }}
        />
      ))}

      {/* Additional gradient overlay for depth */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-transparent via-white/3 to-transparent pointer-events-none"
        style={{ y: parallaxSlow }}
      />
    </div>
  )
}
