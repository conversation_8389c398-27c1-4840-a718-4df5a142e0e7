"use client"

import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { FloatingOrbs } from "@/components/ui/floating-orbs"
import {
  Smartphone,
  QrCode,
  Shield,
  Wallet,
  Globe,
  Zap,
  Database,
  BarChart3,
  Code,
  Lock,
  Users,
  ArrowUpRight
} from "lucide-react"

export function FeaturesSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const features = [
    {
      icon: QrCode,
      title: "NFC/QR Scanning",
      description: "Advanced scanning technology for instant product verification and redemption with mobile apps for iOS and Android.",
      color: "purple",
      stats: "99.9% accuracy"
    },
    {
      icon: Wallet,
      title: "Smart Wallet Creation",
      description: "Automatic smart contract wallet generation using Account Abstraction, eliminating seed phrase complexity.",
      color: "indigo",
      stats: "Sub-second creation"
    },
    {
      icon: Globe,
      title: "ENS Integration",
      description: "Auto-assigned ENS names and customizable subdomains for seamless Web3 identity management.",
      color: "purple",
      stats: "Custom domains"
    },
    {
      icon: Code,
      title: "Developer APIs",
      description: "RESTful APIs with comprehensive documentation, SDKs, and examples for rapid integration.",
      color: "indigo",
      stats: "99.9% uptime"
    },
    {
      icon: Shield,
      title: "Anti-Counterfeit Protection",
      description: "Blockchain-based verification system that makes product counterfeiting virtually impossible.",
      color: "purple",
      stats: "Zero false positives"
    },
    {
      icon: BarChart3,
      title: "Real-time Analytics",
      description: "Comprehensive dashboards with insights on user behavior, token activity, and system performance.",
      color: "indigo",
      stats: "Live monitoring"
    }
  ]

  const capabilities = [
    {
      title: "Multi-Chain Support",
      description: "Deploy across Ethereum, Polygon, and other EVM-compatible networks",
      icon: Database
    },
    {
      title: "Social Login Integration",
      description: "Support for 6+ authentication methods including Twitter, Google, and Discord",
      icon: Users
    },
    {
      title: "Enterprise Security",
      description: "Bank-grade security with SOC 2 compliance and end-to-end encryption",
      icon: Lock
    },
    {
      title: "Scalable Infrastructure",
      description: "Auto-scaling architecture supporting millions of users and transactions",
      icon: Zap
    }
  ]

  return (
    <section id="features" className="relative py-24 bg-white overflow-hidden">
      <FloatingOrbs count={8} className="z-0" />
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="text-center mb-16"
        >
          <motion.h2 
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
          >
            Powerful Features for
            <br />
            <span className="text-gradient">Modern Identity Infrastructure</span>
          </motion.h2>
          <motion.p 
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-3xl mx-auto"
          >
            Built with cutting-edge technology to deliver seamless experiences for both developers and end users.
          </motion.p>
        </motion.div>

        {/* Main Features Grid */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20"
        >
          {features.map((feature, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card className="h-full glass-effect border-2 border-gray-100 hover:border-purple-200 hover-lift hover-glow scroll-reveal group">
                <CardHeader>
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center mb-4 hover-scale ${
                    feature.color === 'purple'
                      ? 'bg-purple-100 group-hover:bg-purple-200'
                      : 'bg-indigo-100 group-hover:bg-indigo-200'
                  } transition-colors duration-300`}>
                    <feature.icon className={`w-6 h-6 ${
                      feature.color === 'purple' ? 'text-purple-600' : 'text-indigo-600'
                    }`} />
                  </div>
                  <CardTitle className="text-xl font-bold text-gray-900">
                    {feature.title}
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className={`text-sm font-semibold ${
                      feature.color === 'purple' ? 'text-purple-600' : 'text-indigo-600'
                    }`}>
                      {feature.stats}
                    </span>
                    <ArrowUpRight className="w-4 h-4 text-gray-400 group-hover:text-purple-600 transition-colors duration-300" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Capabilities */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-3xl p-8 md:p-12"
        >
          <motion.div variants={itemVariants} className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Enterprise-Grade Capabilities
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Built to scale with your business, from startup to enterprise, with the reliability and security you need.
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {capabilities.map((capability, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/50 hover:bg-white transition-all duration-300"
              >
                <div className="w-10 h-10 bg-gradient-purple rounded-lg flex items-center justify-center mb-4">
                  <capability.icon className="w-5 h-5 text-white" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {capability.title}
                </h4>
                <p className="text-sm text-gray-600">
                  {capability.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Performance Stats */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
        >
          {[
            { value: "99.9%", label: "Uptime SLA" },
            { value: "<100ms", label: "API Response" },
            { value: "50M+", label: "API Calls/Month" },
            { value: "24/7", label: "Support" }
          ].map((stat, index) => (
            <motion.div key={index} variants={itemVariants}>
              <div className="text-3xl md:text-4xl font-bold text-gradient mb-2">
                {stat.value}
              </div>
              <div className="text-gray-600 font-medium">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
