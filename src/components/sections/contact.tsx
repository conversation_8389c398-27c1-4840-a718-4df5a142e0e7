"use client"

import { motion } from "framer-motion"
import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Mail,
  MessageSquare,
  Calendar,
  Users,
  ArrowRight,
  CheckCircle,
  Building,
  Code,
  Zap
} from "lucide-react"

export function ContactSection() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    role: "",
    message: "",
    interest: "general"
  })
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    setIsSubmitted(true)
    setTimeout(() => setIsSubmitted(false), 3000)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const contactOptions = [
    {
      icon: Calendar,
      title: "Schedule a Demo",
      description: "Book a personalized demo to see CREFY in action",
      action: "Book Demo",
      color: "purple"
    },
    {
      icon: MessageSquare,
      title: "Technical Support",
      description: "Get help with integration and technical questions",
      action: "Contact Support",
      color: "indigo"
    },
    {
      icon: Users,
      title: "Partnership Inquiry",
      description: "Explore partnership opportunities and collaborations",
      action: "Partner with Us",
      color: "purple"
    }
  ]

  return (
    <section id="contact" className="relative py-24 bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold text-black mb-6"
          >
            Ready to Get
            <br />
            <span className="text-gradient">Started?</span>
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-black max-w-3xl mx-auto"
          >
            Join the future of identity infrastructure. Whether you're a developer, enterprise, 
            or partner, we're here to help you succeed.
          </motion.p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Contact Form */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={itemVariants}
          >
            <Card className="glass-effect border-2 border-purple-100 hover-lift scroll-reveal">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-black flex items-center">
                  <Mail className="w-6 h-6 mr-3 text-purple-600" />
                  Get in Touch
                </CardTitle>
                <CardDescription className="text-black">
                  Tell us about your project and we'll get back to you within 24 hours.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isSubmitted ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="text-center py-8"
                  >
                    <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      Message Sent Successfully!
                    </h3>
                    <p className="text-gray-600">
                      We'll get back to you within 24 hours.
                    </p>
                  </motion.div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Full Name *
                        </label>
                        <input
                          type="text"
                          name="name"
                          required
                          value={formData.name}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          placeholder="John Doe"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Email Address *
                        </label>
                        <input
                          type="email"
                          name="email"
                          required
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Company
                        </label>
                        <input
                          type="text"
                          name="company"
                          value={formData.company}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          placeholder="Your Company"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Role
                        </label>
                        <select
                          name="role"
                          value={formData.role}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        >
                          <option value="">Select Role</option>
                          <option value="developer">Developer</option>
                          <option value="cto">CTO</option>
                          <option value="product-manager">Product Manager</option>
                          <option value="founder">Founder</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Interest
                      </label>
                      <select
                        name="interest"
                        value={formData.interest}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      >
                        <option value="general">General Inquiry</option>
                        <option value="phygital">Crefy Phygital</option>
                        <option value="connect">Crefy Connect</option>
                        <option value="enterprise">Enterprise Solutions</option>
                        <option value="partnership">Partnership</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Message *
                      </label>
                      <textarea
                        name="message"
                        required
                        rows={4}
                        value={formData.message}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none"
                        placeholder="Tell us about your project and how we can help..."
                      />
                    </div>

                    <Button 
                      type="submit"
                      className="w-full bg-gradient-purple hover:opacity-90 text-white py-3 text-lg font-semibold"
                    >
                      Send Message
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </form>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Contact Options */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
            className="space-y-6"
          >
            {contactOptions.map((option, index) => (
              <motion.div key={index} variants={itemVariants}>
                <Card className="border-2 border-gray-100 hover:border-purple-200 transition-all duration-300 hover:shadow-lg group cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        option.color === 'purple' 
                          ? 'bg-purple-100 group-hover:bg-purple-200' 
                          : 'bg-indigo-100 group-hover:bg-indigo-200'
                      } transition-colors duration-300`}>
                        <option.icon className={`w-6 h-6 ${
                          option.color === 'purple' ? 'text-purple-600' : 'text-indigo-600'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          {option.title}
                        </h3>
                        <p className="text-gray-600 mb-4">
                          {option.description}
                        </p>
                        <div className="flex items-center text-purple-600 group-hover:text-purple-700 transition-colors duration-300">
                          <span className="font-medium">{option.action}</span>
                          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}

            {/* Quick Stats */}
            <motion.div variants={itemVariants}>
              <Card className="bg-gradient-to-br from-purple-600 to-indigo-600 text-white border-0">
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-4">Why Choose CREFY?</h3>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Building className="w-5 h-5" />
                      <span>Trusted by 50+ companies</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Code className="w-5 h-5" />
                      <span>Developer-first approach</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Zap className="w-5 h-5" />
                      <span>99.9% uptime SLA</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
