"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Code, Shield, Zap } from "lucide-react"

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-white">

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-screen py-20">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8 text-left"
          >
            {/* Brand Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center space-x-2 glass-effect px-4 py-2 rounded-full"
            >
              <div className="w-2 h-2 bg-gradient-purple rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-black">Next-Gen Identity Infrastructure</span>
            </motion.div>

            {/* Main Headline */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-5xl md:text-6xl lg:text-7xl font-bold text-black leading-tight"
            >
              Build the Future of
              <br />
              <span className="text-gradient">Digital Identity</span>
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="text-xl md:text-2xl text-black max-w-2xl leading-relaxed"
            >
              Modular, developer-first tools that enable seamless authentication,
              tokenization, and governance across Web2 and Web3 experiences.
            </motion.p>

            {/* Feature Pills */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="flex flex-wrap gap-3 text-sm"
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center gap-2 glass-effect px-3 py-2 rounded-full hover-glow cursor-pointer"
              >
                <Shield className="w-4 h-4 text-purple-600" />
                <span className="text-black">Physical Product Tokenization</span>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center gap-2 glass-effect px-3 py-2 rounded-full hover-glow cursor-pointer"
              >
                <Zap className="w-4 h-4 text-purple-600" />
                <span className="text-black">Smart Wallet Integration</span>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center gap-2 glass-effect px-3 py-2 rounded-full hover-glow cursor-pointer"
              >
                <Code className="w-4 h-4 text-purple-600" />
                <span className="text-black">Developer-First APIs</span>
              </motion.div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.9 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Button
                size="lg"
                className="bg-gradient-purple text-white px-8 py-4 text-lg font-semibold rounded-xl btn-enhanced hover-lift hover-glow micro-bounce"
              >
                Start Building
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="glass-effect border-2 border-purple-200 hover:border-purple-300 px-8 py-4 text-lg font-semibold rounded-xl hover-lift micro-bounce text-black"
              >
                View Documentation
              </Button>
            </motion.div>
          </motion.div>

          {/* Right Column - Visual Element */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative hidden lg:block"
          >
            <div className="relative">
              {/* Central Hub */}
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 50, repeat: Infinity, ease: "linear" }}
                className="w-64 h-64 mx-auto relative"
              >
                <div className="absolute inset-0 glass-effect-strong rounded-full border-2 border-purple-200 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gradient mb-2">CREFY</div>
                    <div className="text-sm text-black">Identity Hub</div>
                  </div>
                </div>
              </motion.div>

              {/* Orbiting Elements */}
              <motion.div
                animate={{ rotate: -360 }}
                transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
                className="absolute inset-0"
              >
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="glass-effect w-16 h-16 rounded-full flex items-center justify-center border border-purple-200">
                    <Shield className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2">
                  <div className="glass-effect w-16 h-16 rounded-full flex items-center justify-center border border-purple-200">
                    <Zap className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
                <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="glass-effect w-16 h-16 rounded-full flex items-center justify-center border border-purple-200">
                    <Code className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
                <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2">
                  <div className="glass-effect w-16 h-16 rounded-full flex items-center justify-center border border-purple-200">
                    <ArrowRight className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.1 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto pt-12"
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-gradient">100K+</div>
              <div className="text-gray-600">Tokens Minted</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gradient">50+</div>
              <div className="text-gray-600">Partner Companies</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gradient">99.9%</div>
              <div className="text-gray-600">Uptime SLA</div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-6 h-10 border-2 border-purple-300 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-purple-600 rounded-full mt-2 animate-bounce"></div>
        </div>
      </motion.div>
    </section>
  )
}
