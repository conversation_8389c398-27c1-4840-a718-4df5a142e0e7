@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #7c3aed;
  --primary-foreground: #ffffff;
  --deep-purple: #6b21a8;
  --indigo: #4338ca;
  --royal-purple: #7c3aed;
  --secondary: #ffffff;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f8fafc;
  --accent-foreground: #0f172a;
  --border: #e5e7eb;
  --input: #f9fafb;
  --ring: #7c3aed;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  overflow-x: hidden;
}

/* Enhanced Color Scheme - Only Deep Purple, Indigo, and Royal Purple */
.text-gradient {
  background: linear-gradient(135deg, #6b21a8 0%, #4338ca 50%, #7c3aed 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bg-gradient-purple {
  background: linear-gradient(135deg, #6b21a8 0%, #4338ca 50%, #7c3aed 100%);
}

.bg-gradient-purple-light {
  background: linear-gradient(135deg, rgba(107, 33, 168, 0.1) 0%, rgba(67, 56, 202, 0.1) 50%, rgba(124, 58, 237, 0.1) 100%);
}

.border-gradient {
  border: 1px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #6b21a8, #4338ca, #7c3aed) border-box;
}

/* Advanced Glassmorphism Effects */
.glass-effect {
  backdrop-filter: blur(16px) saturate(180%);
  background: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(107, 33, 168, 0.1);
}

.glass-effect-strong {
  backdrop-filter: blur(20px) saturate(200%);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 12px 40px rgba(107, 33, 168, 0.15);
}

.glass-effect-dark {
  backdrop-filter: blur(16px) saturate(180%);
  background: rgba(107, 33, 168, 0.1);
  border: 1px solid rgba(124, 58, 237, 0.2);
  box-shadow: 0 8px 32px rgba(67, 56, 202, 0.1);
}

/* Floating Orb Animations */
@keyframes float-slow {
  0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
  25% { transform: translateY(-20px) translateX(10px) rotate(90deg); }
  50% { transform: translateY(-10px) translateX(-15px) rotate(180deg); }
  75% { transform: translateY(-30px) translateX(5px) rotate(270deg); }
}

@keyframes float-medium {
  0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
  33% { transform: translateY(-25px) translateX(-20px) rotate(120deg); }
  66% { transform: translateY(15px) translateX(25px) rotate(240deg); }
}

@keyframes float-fast {
  0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
  20% { transform: translateY(-15px) translateX(20px) rotate(72deg); }
  40% { transform: translateY(10px) translateX(-10px) rotate(144deg); }
  60% { transform: translateY(-20px) translateX(-25px) rotate(216deg); }
  80% { transform: translateY(25px) translateX(15px) rotate(288deg); }
}

@keyframes pulse-glow {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Advanced Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(107, 33, 168, 0.15);
}

.hover-glow {
  transition: all 0.3s ease;
  position: relative;
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(124, 58, 237, 0.4), 0 0 60px rgba(67, 56, 202, 0.2);
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-gradient {
  background-size: 200% 200%;
  transition: all 0.3s ease;
}

.hover-gradient:hover {
  animation: gradient-shift 2s ease infinite;
}

/* Parallax and Scroll Effects */
.parallax-slow {
  transform: translateZ(0);
  will-change: transform;
}

.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced Button Effects */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(107, 33, 168, 0.3);
}

/* Micro-interactions */
.micro-bounce {
  transition: transform 0.1s ease;
}

.micro-bounce:active {
  transform: scale(0.98);
}

/* Floating Orb Classes */
.floating-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  mix-blend-mode: multiply;
  pointer-events: none;
  z-index: 0;
}

.floating-orb-1 {
  animation: float-slow 20s ease-in-out infinite;
}

.floating-orb-2 {
  animation: float-medium 15s ease-in-out infinite reverse;
}

.floating-orb-3 {
  animation: float-fast 12s ease-in-out infinite;
}

.floating-orb-4 {
  animation: float-slow 18s ease-in-out infinite reverse;
}

.floating-orb-5 {
  animation: float-medium 22s ease-in-out infinite;
}

/* Performance Optimizations */
.floating-orb {
  will-change: transform;
  transform: translateZ(0);
}

.hover-lift,
.hover-scale,
.hover-glow {
  will-change: transform, box-shadow;
}

/* Responsive Design Helpers */
@media (prefers-reduced-motion: reduce) {
  .floating-orb,
  .hover-lift,
  .hover-scale,
  .scroll-reveal,
  .btn-enhanced {
    animation: none !important;
    transition: none !important;
  }

  .floating-orb {
    opacity: 0.1 !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-effect,
  .glass-effect-strong,
  .glass-effect-dark {
    background: rgba(255, 255, 255, 0.95) !important;
    border: 2px solid #000 !important;
  }

  .text-gradient {
    background: none !important;
    color: #000 !important;
    -webkit-text-fill-color: unset !important;
  }
}

/* Focus styles for accessibility */
.hover-lift:focus-visible,
.hover-scale:focus-visible,
.btn-enhanced:focus-visible {
  outline: 2px solid #7c3aed;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .floating-orb,
  .glass-effect,
  .hover-lift,
  .hover-scale {
    display: none !important;
  }
}
