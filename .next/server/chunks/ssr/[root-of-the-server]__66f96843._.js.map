{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n\nexport function scrollToSection(sectionId: string) {\n  const element = document.getElementById(sectionId)\n  if (element) {\n    element.scrollIntoView({ behavior: 'smooth' })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAEO,SAAS,gBAAgB,SAAiB;IAC/C,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,QAAQ,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9C;AACF", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/navigation/navbar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { motion } from \"framer-motion\"\nimport { Button } from \"@/components/ui/button\"\nimport { Menu, X, Code, FileText, Users, Mail } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nexport function Navbar() {\n  const [isScrolled, setIsScrolled] = useState(false)\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50)\n    }\n    window.addEventListener(\"scroll\", handleScroll)\n    return () => window.removeEventListener(\"scroll\", handleScroll)\n  }, [])\n\n  const navItems = [\n    { name: \"Products\", href: \"#products\", icon: Code },\n    { name: \"Features\", href: \"#features\", icon: FileText },\n    { name: \"Developers\", href: \"#developers\", icon: Users },\n    { name: \"Contact\", href: \"#contact\", icon: Mail },\n  ]\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href)\n    if (element) {\n      element.scrollIntoView({ behavior: \"smooth\" })\n    }\n    setIsMobileMenuOpen(false)\n  }\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6 }}\n      className={cn(\n        \"fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl\",\n        isScrolled\n          ? \"glass-effect-strong shadow-lg border border-purple-100/50\"\n          : \"glass-effect border border-white/30\"\n      )}\n    >\n      <div className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-10\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center\"\n          >\n            <div className=\"text-2xl font-bold text-gradient cursor-pointer hover-scale\">\n              CREFY\n            </div>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <motion.button\n                key={item.name}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => scrollToSection(item.href)}\n                className=\"flex items-center space-x-1 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium\"\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </motion.button>\n            ))}\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"outline\" className=\"glass-effect border-purple-200 hover:border-purple-300 hover-lift micro-bounce\">\n              Sign In\n            </Button>\n            <Button className=\"bg-gradient-purple text-white btn-enhanced hover-lift micro-bounce\">\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"text-gray-700\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden border-t border-purple-100 bg-white/95 backdrop-blur-md\"\n          >\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navItems.map((item) => (\n                <button\n                  key={item.name}\n                  onClick={() => scrollToSection(item.href)}\n                  className=\"flex items-center space-x-2 w-full text-left px-3 py-2 text-gray-700 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors duration-200\"\n                >\n                  <item.icon className=\"w-4 h-4\" />\n                  <span>{item.name}</span>\n                </button>\n              ))}\n              <div className=\"pt-4 space-y-2\">\n                <Button variant=\"outline\" className=\"w-full border-purple-200\">\n                  Sign In\n                </Button>\n                <Button className=\"w-full bg-gradient-purple text-white\">\n                  Get Started\n                </Button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </motion.nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,kMAAA,CAAA,OAAI;QAAC;QAClD;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,8MAAA,CAAA,WAAQ;QAAC;QACtD;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,oMAAA,CAAA,QAAK;QAAC;QACvD;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,kMAAA,CAAA,OAAI;QAAC;KACjD;IAED,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,oBAAoB;IACtB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2EACA,aACI,8DACA;kBAGN,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;0CAA8D;;;;;;;;;;;sCAM/E,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,gBAAgB,KAAK,IAAI;oCACxC,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCAPX,KAAK,IAAI;;;;;;;;;;sCAapB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CAAiF;;;;;;8CAGrH,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAqE;;;;;;;;;;;;sCAMzF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAET,iCACC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAOvB,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;oCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;oCACxC,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;0CAQlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAA2B;;;;;;kDAG/D,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzE", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/ui/floating-orbs.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from \"react\"\n\ninterface Orb {\n  id: number\n  size: number\n  x: number\n  y: number\n  color: string\n  animationClass: string\n  opacity: number\n  delay: number\n}\n\ninterface FloatingOrbsProps {\n  count?: number\n  className?: string\n}\n\nexport function FloatingOrbs({ count = 8, className = \"\" }: FloatingOrbsProps) {\n  const [orbs, setOrbs] = useState<Orb[]>([])\n\n  useEffect(() => {\n    const colors = [\n      'rgba(107, 33, 168, 0.3)',   // deep-purple\n      'rgba(67, 56, 202, 0.3)',    // indigo\n      'rgba(124, 58, 237, 0.3)',   // royal-purple\n      'rgba(107, 33, 168, 0.2)',   // deep-purple lighter\n      'rgba(67, 56, 202, 0.2)',    // indigo lighter\n    ]\n\n    const animationClasses = [\n      'floating-orb-1',\n      'floating-orb-2', \n      'floating-orb-3',\n      'floating-orb-4',\n      'floating-orb-5'\n    ]\n\n    const generateOrbs = () => {\n      const newOrbs: Orb[] = []\n      \n      for (let i = 0; i < count; i++) {\n        newOrbs.push({\n          id: i,\n          size: Math.random() * 300 + 100, // 100px to 400px\n          x: Math.random() * 100, // 0% to 100%\n          y: Math.random() * 100, // 0% to 100%\n          color: colors[Math.floor(Math.random() * colors.length)],\n          animationClass: animationClasses[Math.floor(Math.random() * animationClasses.length)],\n          opacity: Math.random() * 0.4 + 0.1, // 0.1 to 0.5\n          delay: Math.random() * 5, // 0 to 5 seconds delay\n        })\n      }\n      \n      setOrbs(newOrbs)\n    }\n\n    generateOrbs()\n  }, [count])\n\n  return (\n    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>\n      {orbs.map((orb) => (\n        <div\n          key={orb.id}\n          className={`floating-orb ${orb.animationClass}`}\n          style={{\n            width: `${orb.size}px`,\n            height: `${orb.size}px`,\n            left: `${orb.x}%`,\n            top: `${orb.y}%`,\n            backgroundColor: orb.color,\n            opacity: orb.opacity,\n            animationDelay: `${orb.delay}s`,\n            transform: `translate(-50%, -50%)`,\n          }}\n        />\n      ))}\n      \n      {/* Additional gradient overlay for depth */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-transparent pointer-events-none\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAoBO,SAAS,aAAa,EAAE,QAAQ,CAAC,EAAE,YAAY,EAAE,EAAqB;IAC3E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS;YACb;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,mBAAmB;YACvB;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,eAAe;YACnB,MAAM,UAAiB,EAAE;YAEzB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,QAAQ,IAAI,CAAC;oBACX,IAAI;oBACJ,MAAM,KAAK,MAAM,KAAK,MAAM;oBAC5B,GAAG,KAAK,MAAM,KAAK;oBACnB,GAAG,KAAK,MAAM,KAAK;oBACnB,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;oBACxD,gBAAgB,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;oBACrF,SAAS,KAAK,MAAM,KAAK,MAAM;oBAC/B,OAAO,KAAK,MAAM,KAAK;gBACzB;YACF;YAEA,QAAQ;QACV;QAEA;IACF,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;YAChF,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;oBAEC,WAAW,CAAC,aAAa,EAAE,IAAI,cAAc,EAAE;oBAC/C,OAAO;wBACL,OAAO,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC;wBACtB,QAAQ,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC;wBACvB,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBACjB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBAChB,iBAAiB,IAAI,KAAK;wBAC1B,SAAS,IAAI,OAAO;wBACpB,gBAAgB,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;wBAC/B,WAAW,CAAC,qBAAqB,CAAC;oBACpC;mBAXK,IAAI,EAAE;;;;;0BAgBf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/hero.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { FloatingOrbs } from \"@/components/ui/floating-orbs\"\nimport { ArrowRight, Code, Shield, Zap } from \"lucide-react\"\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-white\">\n      {/* Enhanced Floating Orb Background */}\n      <FloatingOrbs count={12} className=\"z-0\" />\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          {/* Logo/Brand */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"flex justify-center\"\n          >\n            <div className=\"text-4xl font-bold text-gradient hover-scale cursor-pointer\">CREFY</div>\n          </motion.div>\n\n          {/* Main Headline */}\n          <motion.h1\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.3 }}\n            className=\"text-5xl md:text-7xl font-bold text-gray-900 leading-tight\"\n          >\n            Foundational Identity\n            <br />\n            <span className=\"text-gradient\">Infrastructure</span>\n            <br />\n            for the Internet of Value\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed\"\n          >\n            Modular, developer-first identity tools that enable seamless authentication, \n            connection, and governance for users across on-chain and off-chain experiences.\n          </motion.p>\n\n          {/* Feature Pills */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.7 }}\n            className=\"flex flex-wrap justify-center gap-4 text-sm\"\n          >\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"flex items-center gap-2 glass-effect px-4 py-2 rounded-full hover-glow cursor-pointer\"\n            >\n              <Shield className=\"w-4 h-4 text-purple-600\" />\n              <span>Tokenized Physical Products</span>\n            </motion.div>\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"flex items-center gap-2 glass-effect px-4 py-2 rounded-full hover-glow cursor-pointer\"\n            >\n              <Zap className=\"w-4 h-4 text-purple-600\" />\n              <span>Smart Wallet Social Login</span>\n            </motion.div>\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"flex items-center gap-2 glass-effect px-4 py-2 rounded-full hover-glow cursor-pointer\"\n            >\n              <Code className=\"w-4 h-4 text-purple-600\" />\n              <span>Developer-First APIs</span>\n            </motion.div>\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.9 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n          >\n            <Button\n              size=\"lg\"\n              className=\"bg-gradient-purple text-white px-8 py-4 text-lg font-semibold rounded-xl btn-enhanced hover-lift hover-glow micro-bounce\"\n            >\n              Start Building\n              <ArrowRight className=\"ml-2 w-5 h-5\" />\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              className=\"glass-effect border-2 border-purple-200 hover:border-purple-300 px-8 py-4 text-lg font-semibold rounded-xl hover-lift micro-bounce\"\n            >\n              View Documentation\n            </Button>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.1 }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto pt-12\"\n          >\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gradient\">100K+</div>\n              <div className=\"text-gray-600\">Tokens Minted</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gradient\">50+</div>\n              <div className=\"text-gray-600\">Partner Companies</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gradient\">99.9%</div>\n              <div className=\"text-gray-600\">Uptime SLA</div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1.5 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <div className=\"w-6 h-10 border-2 border-purple-300 rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-purple-600 rounded-full mt-2 animate-bounce\"></div>\n        </div>\n      </motion.div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC,4IAAA,CAAA,eAAY;gBAAC,OAAO;gBAAI,WAAU;;;;;;0BAEnC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;0CAA8D;;;;;;;;;;;sCAI/E,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;gCACX;8CAEC,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;;;;;gCAAK;;;;;;;sCAKR,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,WAAU;;sDAEV,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;gBACtC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/products.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { FloatingOrbs } from \"@/components/ui/floating-orbs\"\nimport {\n  Smartphone,\n  QrCode,\n  Shield,\n  Users,\n  Wallet,\n  Globe,\n  ArrowRight,\n  Zap,\n  Database,\n  BarChart3\n} from \"lucide-react\"\n\nexport function ProductsSection() {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3\n      }\n    }\n  }\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  }\n\n  return (\n    <section id=\"products\" className=\"relative py-24 bg-white overflow-hidden\">\n      <FloatingOrbs count={10} className=\"z-0\" />\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2 \n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\"\n          >\n            Two Powerful Products,\n            <br />\n            <span className=\"text-gradient\">One Identity Platform</span>\n          </motion.h2>\n          <motion.p \n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto\"\n          >\n            CREFY's modular approach delivers specialized solutions for physical product tokenization \n            and seamless Web3 onboarding, built on a unified identity infrastructure.\n          </motion.p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\n          {/* Crefy Phygital */}\n          <motion.div\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true }}\n            variants={itemVariants}\n          >\n            <Card className=\"h-full glass-effect border-2 border-purple-100 hover:border-purple-200 hover-lift hover-glow scroll-reveal\">\n              <CardHeader className=\"text-center pb-8\">\n                <div className=\"w-16 h-16 bg-gradient-purple rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <QrCode className=\"w-8 h-8 text-white\" />\n                </div>\n                <CardTitle className=\"text-3xl font-bold text-gray-900\">\n                  Crefy Phygital\n                </CardTitle>\n                <CardDescription className=\"text-lg text-gray-600\">\n                  Transform physical products into verifiable digital assets with seamless tokenization and lifecycle management.\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-6\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div className=\"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\">\n                    <Smartphone className=\"w-5 h-5 text-purple-600\" />\n                    <span className=\"text-sm font-medium\">Mobile Scanner</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\">\n                    <Shield className=\"w-5 h-5 text-purple-600\" />\n                    <span className=\"text-sm font-medium\">Anti-Counterfeit</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\">\n                    <Database className=\"w-5 h-5 text-purple-600\" />\n                    <span className=\"text-sm font-medium\">Asset Tracking</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\">\n                    <BarChart3 className=\"w-5 h-5 text-purple-600\" />\n                    <span className=\"text-sm font-medium\">Analytics</span>\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <h4 className=\"font-semibold text-gray-900\">Key Features:</h4>\n                  <ul className=\"space-y-2 text-gray-600\">\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>RESTful APIs for token creation, minting, and redemption</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>NFC/QR scanner apps for iOS and Android</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>Company dashboard for asset management</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>User portal for claiming and viewing assets</span>\n                    </li>\n                  </ul>\n                </div>\n\n                <Button className=\"w-full bg-gradient-purple hover:opacity-90 text-white\">\n                  Explore Phygital\n                  <ArrowRight className=\"ml-2 w-4 h-4\" />\n                </Button>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Crefy Connect */}\n          <motion.div\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true }}\n            variants={itemVariants}\n          >\n            <Card className=\"h-full glass-effect border-2 border-indigo-100 hover:border-indigo-200 hover-lift hover-glow scroll-reveal\">\n              <CardHeader className=\"text-center pb-8\">\n                <div className=\"w-16 h-16 bg-gradient-purple rounded-2xl flex items-center justify-center mx-auto mb-4 hover-scale\">\n                  <Wallet className=\"w-8 h-8 text-white\" />\n                </div>\n                <CardTitle className=\"text-3xl font-bold text-gray-900\">\n                  Crefy Connect\n                </CardTitle>\n                <CardDescription className=\"text-lg text-gray-600\">\n                  Seamless social login with embedded smart wallets that abstract away Web3 complexity for users.\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-6\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div className=\"flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg\">\n                    <Users className=\"w-5 h-5 text-indigo-600\" />\n                    <span className=\"text-sm font-medium\">Social Auth</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg\">\n                    <Zap className=\"w-5 h-5 text-indigo-600\" />\n                    <span className=\"text-sm font-medium\">Smart Wallets</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg\">\n                    <Globe className=\"w-5 h-5 text-indigo-600\" />\n                    <span className=\"text-sm font-medium\">ENS Integration</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg\">\n                    <BarChart3 className=\"w-5 h-5 text-indigo-600\" />\n                    <span className=\"text-sm font-medium\">Dev Analytics</span>\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <h4 className=\"font-semibold text-gray-900\">Supported Login Methods:</h4>\n                  <div className=\"grid grid-cols-3 gap-2 text-sm\">\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">Twitter</div>\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">Google</div>\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">Email</div>\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">Phone</div>\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">Discord</div>\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">EOA</div>\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <h4 className=\"font-semibold text-gray-900\">Key Features:</h4>\n                  <ul className=\"space-y-2 text-gray-600\">\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>Account Abstraction with smart contract wallets</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>Auto-assigned ENS names and subdomains</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>Integrated fiat on/off ramps</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>Developer analytics and monitoring</span>\n                    </li>\n                  </ul>\n                </div>\n\n                <Button className=\"w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:opacity-90 text-white\">\n                  Explore Connect\n                  <ArrowRight className=\"ml-2 w-4 h-4\" />\n                </Button>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAmBO,SAAS;IACd,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;;0BAC/B,8OAAC,4IAAA,CAAA,eAAY;gBAAC,OAAO;gBAAI,WAAU;;;;;;0BACnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,UAAU;wBACV,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,UAAU;gCACV,WAAU;;oCACX;kDAEC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAQ;gCACR,aAAY;gCACZ,UAAU;oCAAE,MAAM;gCAAK;gCACvB,UAAU;0CAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAmC;;;;;;8DAGxD,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DAAwB;;;;;;;;;;;;sDAIrD,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8MAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;;;;;;;8DAI1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA8B;;;;;;sEAC5C,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;8DAKZ,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;;wDAAwD;sEAExE,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAQ;gCACR,aAAY;gCACZ,UAAU;oCAAE,MAAM;gCAAK;gCACvB,UAAU;0CAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAmC;;;;;;8DAGxD,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DAAwB;;;;;;;;;;;;sDAIrD,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;;;;;;;8DAI1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA8B;;;;;;sEAC5C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,8OAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,8OAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,8OAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,8OAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,8OAAC;oEAAI,WAAU;8EAAqC;;;;;;;;;;;;;;;;;;8DAIxD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA8B;;;;;;sEAC5C,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;8DAKZ,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;;wDAAoF;sEAEpG,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC", "debugId": null}}, {"offset": {"line": 1816, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/features.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { FloatingOrbs } from \"@/components/ui/floating-orbs\"\nimport {\n  Smartphone,\n  QrCode,\n  Shield,\n  Wallet,\n  Globe,\n  Zap,\n  Database,\n  BarChart3,\n  Code,\n  Lock,\n  Users,\n  ArrowUpRight\n} from \"lucide-react\"\n\nexport function FeaturesSection() {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  }\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  }\n\n  const features = [\n    {\n      icon: QrCode,\n      title: \"NFC/QR Scanning\",\n      description: \"Advanced scanning technology for instant product verification and redemption with mobile apps for iOS and Android.\",\n      color: \"purple\",\n      stats: \"99.9% accuracy\"\n    },\n    {\n      icon: Wallet,\n      title: \"Smart Wallet Creation\",\n      description: \"Automatic smart contract wallet generation using Account Abstraction, eliminating seed phrase complexity.\",\n      color: \"indigo\",\n      stats: \"Sub-second creation\"\n    },\n    {\n      icon: Globe,\n      title: \"ENS Integration\",\n      description: \"Auto-assigned ENS names and customizable subdomains for seamless Web3 identity management.\",\n      color: \"purple\",\n      stats: \"Custom domains\"\n    },\n    {\n      icon: Code,\n      title: \"Developer APIs\",\n      description: \"RESTful APIs with comprehensive documentation, SDKs, and examples for rapid integration.\",\n      color: \"indigo\",\n      stats: \"99.9% uptime\"\n    },\n    {\n      icon: Shield,\n      title: \"Anti-Counterfeit Protection\",\n      description: \"Blockchain-based verification system that makes product counterfeiting virtually impossible.\",\n      color: \"purple\",\n      stats: \"Zero false positives\"\n    },\n    {\n      icon: BarChart3,\n      title: \"Real-time Analytics\",\n      description: \"Comprehensive dashboards with insights on user behavior, token activity, and system performance.\",\n      color: \"indigo\",\n      stats: \"Live monitoring\"\n    }\n  ]\n\n  const capabilities = [\n    {\n      title: \"Multi-Chain Support\",\n      description: \"Deploy across Ethereum, Polygon, and other EVM-compatible networks\",\n      icon: Database\n    },\n    {\n      title: \"Social Login Integration\",\n      description: \"Support for 6+ authentication methods including Twitter, Google, and Discord\",\n      icon: Users\n    },\n    {\n      title: \"Enterprise Security\",\n      description: \"Bank-grade security with SOC 2 compliance and end-to-end encryption\",\n      icon: Lock\n    },\n    {\n      title: \"Scalable Infrastructure\",\n      description: \"Auto-scaling architecture supporting millions of users and transactions\",\n      icon: Zap\n    }\n  ]\n\n  return (\n    <section id=\"features\" className=\"relative py-24 bg-white overflow-hidden\">\n      <FloatingOrbs count={8} className=\"z-0\" />\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2 \n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\"\n          >\n            Powerful Features for\n            <br />\n            <span className=\"text-gradient\">Modern Identity Infrastructure</span>\n          </motion.h2>\n          <motion.p \n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto\"\n          >\n            Built with cutting-edge technology to deliver seamless experiences for both developers and end users.\n          </motion.p>\n        </motion.div>\n\n        {/* Main Features Grid */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\"\n        >\n          {features.map((feature, index) => (\n            <motion.div key={index} variants={itemVariants}>\n              <Card className=\"h-full glass-effect border-2 border-gray-100 hover:border-purple-200 hover-lift hover-glow scroll-reveal group\">\n                <CardHeader>\n                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center mb-4 hover-scale ${\n                    feature.color === 'purple'\n                      ? 'bg-purple-100 group-hover:bg-purple-200'\n                      : 'bg-indigo-100 group-hover:bg-indigo-200'\n                  } transition-colors duration-300`}>\n                    <feature.icon className={`w-6 h-6 ${\n                      feature.color === 'purple' ? 'text-purple-600' : 'text-indigo-600'\n                    }`} />\n                  </div>\n                  <CardTitle className=\"text-xl font-bold text-gray-900\">\n                    {feature.title}\n                  </CardTitle>\n                  <CardDescription className=\"text-gray-600\">\n                    {feature.description}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"flex items-center justify-between\">\n                    <span className={`text-sm font-semibold ${\n                      feature.color === 'purple' ? 'text-purple-600' : 'text-indigo-600'\n                    }`}>\n                      {feature.stats}\n                    </span>\n                    <ArrowUpRight className=\"w-4 h-4 text-gray-400 group-hover:text-purple-600 transition-colors duration-300\" />\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Additional Capabilities */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-3xl p-8 md:p-12\"\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-12\">\n            <h3 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Enterprise-Grade Capabilities\n            </h3>\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n              Built to scale with your business, from startup to enterprise, with the reliability and security you need.\n            </p>\n          </motion.div>\n\n          <motion.div\n            variants={containerVariants}\n            className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\"\n          >\n            {capabilities.map((capability, index) => (\n              <motion.div\n                key={index}\n                variants={itemVariants}\n                className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/50 hover:bg-white transition-all duration-300\"\n              >\n                <div className=\"w-10 h-10 bg-gradient-purple rounded-lg flex items-center justify-center mb-4\">\n                  <capability.icon className=\"w-5 h-5 text-white\" />\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">\n                  {capability.title}\n                </h4>\n                <p className=\"text-sm text-gray-600\">\n                  {capability.description}\n                </p>\n              </motion.div>\n            ))}\n          </motion.div>\n        </motion.div>\n\n        {/* Performance Stats */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center\"\n        >\n          {[\n            { value: \"99.9%\", label: \"Uptime SLA\" },\n            { value: \"<100ms\", label: \"API Response\" },\n            { value: \"50M+\", label: \"API Calls/Month\" },\n            { value: \"24/7\", label: \"Support\" }\n          ].map((stat, index) => (\n            <motion.div key={index} variants={itemVariants}>\n              <div className=\"text-3xl md:text-4xl font-bold text-gradient mb-2\">\n                {stat.value}\n              </div>\n              <div className=\"text-gray-600 font-medium\">\n                {stat.label}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAoBO,SAAS;IACd,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,WAAW;QACf;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,kNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,eAAe;QACnB;YACE,OAAO;YACP,aAAa;YACb,MAAM,0MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,oMAAA,CAAA,QAAK;QACb;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,gMAAA,CAAA,MAAG;QACX;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;;0BAC/B,8OAAC,4IAAA,CAAA,eAAY;gBAAC,OAAO;gBAAG,WAAU;;;;;;0BAClC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,UAAU;wBACV,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,UAAU;gCACV,WAAU;;oCACX;kDAEC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,UAAU;wBACV,WAAU;kCAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAa,UAAU;0CAChC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC;oDAAI,WAAW,CAAC,uEAAuE,EACtF,QAAQ,KAAK,KAAK,WACd,4CACA,0CACL,+BAA+B,CAAC;8DAC/B,cAAA,8OAAC,QAAQ,IAAI;wDAAC,WAAW,CAAC,QAAQ,EAChC,QAAQ,KAAK,KAAK,WAAW,oBAAoB,mBACjD;;;;;;;;;;;8DAEJ,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,QAAQ,KAAK;;;;;;8DAEhB,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,QAAQ,WAAW;;;;;;;;;;;;sDAGxB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAW,CAAC,sBAAsB,EACtC,QAAQ,KAAK,KAAK,WAAW,oBAAoB,mBACjD;kEACC,QAAQ,KAAK;;;;;;kEAEhB,8OAAC,0NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA1Bf;;;;;;;;;;kCAmCrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,UAAU;wBACV,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;gCAAc,WAAU;;kDAC5C,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;;0CAKzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;0CAET,aAAa,GAAG,CAAC,CAAC,YAAY,sBAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU;wCACV,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,WAAW,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAE7B,8OAAC;gDAAG,WAAU;0DACX,WAAW,KAAK;;;;;;0DAEnB,8OAAC;gDAAE,WAAU;0DACV,WAAW,WAAW;;;;;;;uCAXpB;;;;;;;;;;;;;;;;kCAmBb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,UAAU;wBACV,WAAU;kCAET;4BACC;gCAAE,OAAO;gCAAS,OAAO;4BAAa;4BACtC;gCAAE,OAAO;gCAAU,OAAO;4BAAe;4BACzC;gCAAE,OAAO;gCAAQ,OAAO;4BAAkB;4BAC1C;gCAAE,OAAO;gCAAQ,OAAO;4BAAU;yBACnC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAa,UAAU;;kDAChC,8OAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAEb,8OAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;;+BALE;;;;;;;;;;;;;;;;;;;;;;AAa7B", "debugId": null}}, {"offset": {"line": 2254, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/developers.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { FloatingOrbs } from \"@/components/ui/floating-orbs\"\nimport {\n  Code,\n  Terminal,\n  Book,\n  Zap,\n  Copy,\n  Check,\n  ArrowRight,\n  Github,\n  FileText,\n  Play\n} from \"lucide-react\"\n\nexport function DevelopersSection() {\n  const [activeTab, setActiveTab] = useState(\"phygital\")\n  const [copiedCode, setCopiedCode] = useState<string | null>(null)\n\n  const copyToClipboard = (code: string, id: string) => {\n    navigator.clipboard.writeText(code)\n    setCopiedCode(id)\n    setTimeout(() => setCopiedCode(null), 2000)\n  }\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  }\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  }\n\n  const phygitalCode = `// Initialize Crefy Phygital SDK\nimport { CrefyPhygital } from '@crefy/phygital-sdk'\n\nconst crefy = new CrefyPhygital({\n  apiKey: 'your-api-key',\n  environment: 'production'\n})\n\n// Create a new tokenized product\nconst product = await crefy.products.create({\n  name: 'Limited Edition Sneaker',\n  description: 'Exclusive drop with NFC authentication',\n  metadata: {\n    brand: 'YourBrand',\n    model: 'AirMax2024',\n    size: '10',\n    color: 'Black/Purple'\n  },\n  supply: 1000\n})\n\n// Mint tokens for physical products\nconst tokens = await crefy.tokens.mint({\n  productId: product.id,\n  quantity: 100,\n  recipient: '0x...' // Company wallet\n})\n\nconsole.log(\\`Minted \\${tokens.length} tokens\\`)`\n\n  const connectCode = `// Initialize Crefy Connect SDK\nimport { CrefyConnect } from '@crefy/connect-sdk'\n\nconst connect = new CrefyConnect({\n  projectId: 'your-project-id',\n  chains: ['ethereum', 'polygon'],\n  ensSubdomain: 'yourapp.crefy.eth'\n})\n\n// Social login with embedded wallet\nconst user = await connect.auth.login({\n  provider: 'google', // twitter, discord, email, phone\n  redirectUri: 'https://yourapp.com/callback'\n})\n\n// Access user's smart wallet\nconst wallet = user.wallet\nconst balance = await wallet.getBalance()\nconst address = wallet.getAddress()\n\n// Send transaction (gasless with AA)\nconst tx = await wallet.sendTransaction({\n  to: '0x...',\n  value: '0.1',\n  data: '0x...'\n})\n\nconsole.log(\\`Transaction sent: \\${tx.hash}\\`)`\n\n  const resources = [\n    {\n      icon: Book,\n      title: \"API Documentation\",\n      description: \"Comprehensive guides and references for all endpoints\",\n      link: \"/docs/api\",\n      color: \"purple\"\n    },\n    {\n      icon: Code,\n      title: \"SDK Libraries\",\n      description: \"JavaScript, Python, and React SDKs with TypeScript support\",\n      link: \"/docs/sdks\",\n      color: \"indigo\"\n    },\n    {\n      icon: Terminal,\n      title: \"CLI Tools\",\n      description: \"Command-line interface for rapid development and testing\",\n      link: \"/docs/cli\",\n      color: \"purple\"\n    },\n    {\n      icon: Github,\n      title: \"Code Examples\",\n      description: \"Open-source examples and starter templates\",\n      link: \"/examples\",\n      color: \"indigo\"\n    }\n  ]\n\n  return (\n    <section id=\"developers\" className=\"relative py-24 bg-white overflow-hidden\">\n      <FloatingOrbs count={6} className=\"z-0\" />\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\"\n          >\n            Built for\n            <br />\n            <span className=\"text-gradient\">Developers</span>\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto\"\n          >\n            Get started in minutes with our developer-first APIs, comprehensive SDKs, \n            and extensive documentation. Build the future of identity infrastructure.\n          </motion.p>\n        </motion.div>\n\n        {/* Code Examples */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"mb-20\"\n        >\n          <motion.div variants={itemVariants} className=\"mb-8\">\n            <div className=\"flex justify-center space-x-4 mb-8\">\n              <button\n                onClick={() => setActiveTab(\"phygital\")}\n                className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${\n                  activeTab === \"phygital\"\n                    ? \"bg-purple-600 text-white\"\n                    : \"bg-gray-800 text-gray-300 hover:bg-gray-700\"\n                }`}\n              >\n                Crefy Phygital\n              </button>\n              <button\n                onClick={() => setActiveTab(\"connect\")}\n                className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${\n                  activeTab === \"connect\"\n                    ? \"bg-indigo-600 text-white\"\n                    : \"bg-gray-800 text-gray-300 hover:bg-gray-700\"\n                }`}\n              >\n                Crefy Connect\n              </button>\n            </div>\n          </motion.div>\n\n          <motion.div variants={itemVariants}>\n            <Card className=\"bg-gray-900/50 border-gray-700 backdrop-blur-sm\">\n              <CardHeader className=\"flex flex-row items-center justify-between\">\n                <div>\n                  <CardTitle className=\"text-white\">\n                    {activeTab === \"phygital\" ? \"Tokenize Physical Products\" : \"Social Login with Smart Wallets\"}\n                  </CardTitle>\n                  <CardDescription className=\"text-gray-400\">\n                    {activeTab === \"phygital\" \n                      ? \"Create and mint tokenized products with our Phygital SDK\"\n                      : \"Implement seamless Web3 onboarding with Connect SDK\"\n                    }\n                  </CardDescription>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => copyToClipboard(\n                    activeTab === \"phygital\" ? phygitalCode : connectCode,\n                    activeTab\n                  )}\n                  className=\"border-gray-600 text-gray-300 hover:bg-gray-800\"\n                >\n                  {copiedCode === activeTab ? (\n                    <Check className=\"w-4 h-4\" />\n                  ) : (\n                    <Copy className=\"w-4 h-4\" />\n                  )}\n                </Button>\n              </CardHeader>\n              <CardContent>\n                <div className=\"bg-gray-950 rounded-lg p-4 overflow-x-auto\">\n                  <pre className=\"text-sm text-gray-300\">\n                    <code>{activeTab === \"phygital\" ? phygitalCode : connectCode}</code>\n                  </pre>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </motion.div>\n\n        {/* Developer Resources */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"mb-16\"\n        >\n          <motion.h3 \n            variants={itemVariants}\n            className=\"text-3xl font-bold text-center mb-12\"\n          >\n            Developer Resources\n          </motion.h3>\n          \n          <motion.div\n            variants={containerVariants}\n            className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\"\n          >\n            {resources.map((resource, index) => (\n              <motion.div key={index} variants={itemVariants}>\n                <Card className=\"h-full bg-gray-900/30 border-gray-700 hover:border-purple-500 transition-all duration-300 hover:bg-gray-900/50 group cursor-pointer\">\n                  <CardHeader>\n                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center mb-4 ${\n                      resource.color === 'purple' \n                        ? 'bg-purple-600/20 group-hover:bg-purple-600/30' \n                        : 'bg-indigo-600/20 group-hover:bg-indigo-600/30'\n                    } transition-colors duration-300`}>\n                      <resource.icon className={`w-6 h-6 ${\n                        resource.color === 'purple' ? 'text-purple-400' : 'text-indigo-400'\n                      }`} />\n                    </div>\n                    <CardTitle className=\"text-white text-lg\">\n                      {resource.title}\n                    </CardTitle>\n                    <CardDescription className=\"text-gray-400\">\n                      {resource.description}\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"flex items-center text-purple-400 group-hover:text-purple-300 transition-colors duration-300\">\n                      <span className=\"text-sm font-medium\">Learn more</span>\n                      <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </motion.div>\n        </motion.div>\n\n        {/* Quick Start */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"text-center\"\n        >\n          <motion.div \n            variants={itemVariants}\n            className=\"bg-gradient-to-r from-purple-600/20 to-indigo-600/20 rounded-3xl p-8 md:p-12 border border-purple-500/30\"\n          >\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              Ready to Start Building?\n            </h3>\n            <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\n              Join thousands of developers building the future of identity infrastructure. \n              Get started with our free tier and scale as you grow.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button \n                size=\"lg\" \n                className=\"bg-gradient-purple hover:opacity-90 text-white px-8 py-4 text-lg font-semibold\"\n              >\n                <Play className=\"mr-2 w-5 h-5\" />\n                Start Free Trial\n              </Button>\n              <Button \n                variant=\"outline\" \n                size=\"lg\"\n                className=\"border-2 border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-8 py-4 text-lg font-semibold\"\n              >\n                <FileText className=\"mr-2 w-5 h-5\" />\n                View Documentation\n              </Button>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAoBO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,kBAAkB,CAAC,MAAc;QACrC,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,cAAc;QACd,WAAW,IAAM,cAAc,OAAO;IACxC;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;gDA4BwB,CAAC;IAE/C,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;8CA2BuB,CAAC;IAE7C,MAAM,YAAY;QAChB;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAa,WAAU;;0BACjC,8OAAC,4IAAA,CAAA,eAAY;gBAAC,OAAO;gBAAG,WAAU;;;;;;0BAClC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,UAAU;wBACV,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,UAAU;gCACV,WAAU;;oCACX;kDAEC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,UAAU;wBACV,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;gCAAc,WAAU;0CAC5C,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,+DAA+D,EACzE,cAAc,aACV,6BACA,+CACJ;sDACH;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,+DAA+D,EACzE,cAAc,YACV,6BACA,+CACJ;sDACH;;;;;;;;;;;;;;;;;0CAML,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAClB,cAAc,aAAa,+BAA+B;;;;;;sEAE7D,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEACxB,cAAc,aACX,6DACA;;;;;;;;;;;;8DAIR,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,gBACb,cAAc,aAAa,eAAe,aAC1C;oDAEF,WAAU;8DAET,eAAe,0BACd,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;6EAEjB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAItB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAM,cAAc,aAAa,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,UAAU;wBACV,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;0CAET,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAAa,UAAU;kDAChC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC,gIAAA,CAAA,aAAU;;sEACT,8OAAC;4DAAI,WAAW,CAAC,2DAA2D,EAC1E,SAAS,KAAK,KAAK,WACf,kDACA,gDACL,+BAA+B,CAAC;sEAC/B,cAAA,8OAAC,SAAS,IAAI;gEAAC,WAAW,CAAC,QAAQ,EACjC,SAAS,KAAK,KAAK,WAAW,oBAAoB,mBAClD;;;;;;;;;;;sEAEJ,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAClB,SAAS,KAAK;;;;;;sEAEjB,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEACxB,SAAS,WAAW;;;;;;;;;;;;8DAGzB,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;0EACtC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAtBb;;;;;;;;;;;;;;;;kCAgCvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,UAAU;wBACV,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAI5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD", "debugId": null}}, {"offset": {"line": 2831, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/contact.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { FloatingOrbs } from \"@/components/ui/floating-orbs\"\nimport {\n  Mail,\n  MessageSquare,\n  Calendar,\n  Users,\n  ArrowRight,\n  CheckCircle,\n  Building,\n  Code,\n  Zap\n} from \"lucide-react\"\n\nexport function ContactSection() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    company: \"\",\n    role: \"\",\n    message: \"\",\n    interest: \"general\"\n  })\n  const [isSubmitted, setIsSubmitted] = useState(false)\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Handle form submission here\n    setIsSubmitted(true)\n    setTimeout(() => setIsSubmitted(false), 3000)\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  }\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  }\n\n  const contactOptions = [\n    {\n      icon: Calendar,\n      title: \"Schedule a Demo\",\n      description: \"Book a personalized demo to see CREFY in action\",\n      action: \"Book Demo\",\n      color: \"purple\"\n    },\n    {\n      icon: MessageSquare,\n      title: \"Technical Support\",\n      description: \"Get help with integration and technical questions\",\n      action: \"Contact Support\",\n      color: \"indigo\"\n    },\n    {\n      icon: Users,\n      title: \"Partnership Inquiry\",\n      description: \"Explore partnership opportunities and collaborations\",\n      action: \"Partner with Us\",\n      color: \"purple\"\n    }\n  ]\n\n  return (\n    <section id=\"contact\" className=\"relative py-24 bg-white overflow-hidden\">\n      <FloatingOrbs count={6} className=\"z-0\" />\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2 \n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\"\n          >\n            Ready to Get\n            <br />\n            <span className=\"text-gradient\">Started?</span>\n          </motion.h2>\n          <motion.p \n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto\"\n          >\n            Join the future of identity infrastructure. Whether you're a developer, enterprise, \n            or partner, we're here to help you succeed.\n          </motion.p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\n          {/* Contact Form */}\n          <motion.div\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true }}\n            variants={itemVariants}\n          >\n            <Card className=\"glass-effect border-2 border-purple-100 hover-lift scroll-reveal\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-bold text-gray-900 flex items-center\">\n                  <Mail className=\"w-6 h-6 mr-3 text-purple-600\" />\n                  Get in Touch\n                </CardTitle>\n                <CardDescription className=\"text-gray-600\">\n                  Tell us about your project and we'll get back to you within 24 hours.\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                {isSubmitted ? (\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.8 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className=\"text-center py-8\"\n                  >\n                    <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                      Message Sent Successfully!\n                    </h3>\n                    <p className=\"text-gray-600\">\n                      We'll get back to you within 24 hours.\n                    </p>\n                  </motion.div>\n                ) : (\n                  <form onSubmit={handleSubmit} className=\"space-y-6\">\n                    <div className=\"grid md:grid-cols-2 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Full Name *\n                        </label>\n                        <input\n                          type=\"text\"\n                          name=\"name\"\n                          required\n                          value={formData.name}\n                          onChange={handleInputChange}\n                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                          placeholder=\"John Doe\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Email Address *\n                        </label>\n                        <input\n                          type=\"email\"\n                          name=\"email\"\n                          required\n                          value={formData.email}\n                          onChange={handleInputChange}\n                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                          placeholder=\"<EMAIL>\"\n                        />\n                      </div>\n                    </div>\n\n                    <div className=\"grid md:grid-cols-2 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Company\n                        </label>\n                        <input\n                          type=\"text\"\n                          name=\"company\"\n                          value={formData.company}\n                          onChange={handleInputChange}\n                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                          placeholder=\"Your Company\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Role\n                        </label>\n                        <select\n                          name=\"role\"\n                          value={formData.role}\n                          onChange={handleInputChange}\n                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                        >\n                          <option value=\"\">Select Role</option>\n                          <option value=\"developer\">Developer</option>\n                          <option value=\"cto\">CTO</option>\n                          <option value=\"product-manager\">Product Manager</option>\n                          <option value=\"founder\">Founder</option>\n                          <option value=\"other\">Other</option>\n                        </select>\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Interest\n                      </label>\n                      <select\n                        name=\"interest\"\n                        value={formData.interest}\n                        onChange={handleInputChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                      >\n                        <option value=\"general\">General Inquiry</option>\n                        <option value=\"phygital\">Crefy Phygital</option>\n                        <option value=\"connect\">Crefy Connect</option>\n                        <option value=\"enterprise\">Enterprise Solutions</option>\n                        <option value=\"partnership\">Partnership</option>\n                      </select>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Message *\n                      </label>\n                      <textarea\n                        name=\"message\"\n                        required\n                        rows={4}\n                        value={formData.message}\n                        onChange={handleInputChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none\"\n                        placeholder=\"Tell us about your project and how we can help...\"\n                      />\n                    </div>\n\n                    <Button \n                      type=\"submit\"\n                      className=\"w-full bg-gradient-purple hover:opacity-90 text-white py-3 text-lg font-semibold\"\n                    >\n                      Send Message\n                      <ArrowRight className=\"ml-2 w-5 h-5\" />\n                    </Button>\n                  </form>\n                )}\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Contact Options */}\n          <motion.div\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true }}\n            variants={containerVariants}\n            className=\"space-y-6\"\n          >\n            {contactOptions.map((option, index) => (\n              <motion.div key={index} variants={itemVariants}>\n                <Card className=\"border-2 border-gray-100 hover:border-purple-200 transition-all duration-300 hover:shadow-lg group cursor-pointer\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-start space-x-4\">\n                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${\n                        option.color === 'purple' \n                          ? 'bg-purple-100 group-hover:bg-purple-200' \n                          : 'bg-indigo-100 group-hover:bg-indigo-200'\n                      } transition-colors duration-300`}>\n                        <option.icon className={`w-6 h-6 ${\n                          option.color === 'purple' ? 'text-purple-600' : 'text-indigo-600'\n                        }`} />\n                      </div>\n                      <div className=\"flex-1\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                          {option.title}\n                        </h3>\n                        <p className=\"text-gray-600 mb-4\">\n                          {option.description}\n                        </p>\n                        <div className=\"flex items-center text-purple-600 group-hover:text-purple-700 transition-colors duration-300\">\n                          <span className=\"font-medium\">{option.action}</span>\n                          <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n\n            {/* Quick Stats */}\n            <motion.div variants={itemVariants}>\n              <Card className=\"bg-gradient-to-br from-purple-600 to-indigo-600 text-white border-0\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold mb-4\">Why Choose CREFY?</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <Building className=\"w-5 h-5\" />\n                      <span>Trusted by 50+ companies</span>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <Code className=\"w-5 h-5\" />\n                      <span>Developer-first approach</span>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <Zap className=\"w-5 h-5\" />\n                      <span>99.9% uptime SLA</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAmBO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,SAAS;QACT,UAAU;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,eAAe;QACf,WAAW,IAAM,eAAe,QAAQ;IAC1C;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,iBAAiB;QACrB;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;YACb,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,QAAQ;YACR,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;;0BAC9B,8OAAC,4IAAA,CAAA,eAAY;gBAAC,OAAO;gBAAG,WAAU;;;;;;0BAClC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,UAAU;wBACV,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,UAAU;gCACV,WAAU;;oCACX;kDAEC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAQ;gCACR,aAAY;gCACZ,UAAU;oCAAE,MAAM;gCAAK;gCACvB,UAAU;0CAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;8DAGnD,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DAAgB;;;;;;;;;;;;sDAI7C,8OAAC,gIAAA,CAAA,cAAW;sDACT,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,WAAU;;kEAEV,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAG,WAAU;kEAA2C;;;;;;kEAGzD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;qEAK/B,8OAAC;gDAAK,UAAU;gDAAc,WAAU;;kEACtC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,8OAAC;wEACC,MAAK;wEACL,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,IAAI;wEACpB,UAAU;wEACV,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAGhB,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,8OAAC;wEACC,MAAK;wEACL,MAAK;wEACL,QAAQ;wEACR,OAAO,SAAS,KAAK;wEACrB,UAAU;wEACV,WAAU;wEACV,aAAY;;;;;;;;;;;;;;;;;;kEAKlB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,8OAAC;wEACC,MAAK;wEACL,MAAK;wEACL,OAAO,SAAS,OAAO;wEACvB,UAAU;wEACV,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAGhB,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,8OAAC;wEACC,MAAK;wEACL,OAAO,SAAS,IAAI;wEACpB,UAAU;wEACV,WAAU;;0FAEV,8OAAC;gFAAO,OAAM;0FAAG;;;;;;0FACjB,8OAAC;gFAAO,OAAM;0FAAY;;;;;;0FAC1B,8OAAC;gFAAO,OAAM;0FAAM;;;;;;0FACpB,8OAAC;gFAAO,OAAM;0FAAkB;;;;;;0FAChC,8OAAC;gFAAO,OAAM;0FAAU;;;;;;0FACxB,8OAAC;gFAAO,OAAM;0FAAQ;;;;;;;;;;;;;;;;;;;;;;;;kEAK5B,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO,SAAS,QAAQ;gEACxB,UAAU;gEACV,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,8OAAC;wEAAO,OAAM;kFAAW;;;;;;kFACzB,8OAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,8OAAC;wEAAO,OAAM;kFAAa;;;;;;kFAC3B,8OAAC;wEAAO,OAAM;kFAAc;;;;;;;;;;;;;;;;;;kEAIhC,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,QAAQ;gEACR,MAAM;gEACN,OAAO,SAAS,OAAO;gEACvB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,WAAU;;4DACX;0EAEC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAQ;gCACR,aAAY;gCACZ,UAAU;oCAAE,MAAM;gCAAK;gCACvB,UAAU;gCACV,WAAU;;oCAET,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAAa,UAAU;sDAChC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,sDAAsD,EACrE,OAAO,KAAK,KAAK,WACb,4CACA,0CACL,+BAA+B,CAAC;0EAC/B,cAAA,8OAAC,OAAO,IAAI;oEAAC,WAAW,CAAC,QAAQ,EAC/B,OAAO,KAAK,KAAK,WAAW,oBAAoB,mBAChD;;;;;;;;;;;0EAEJ,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFACX,OAAO,KAAK;;;;;;kFAEf,8OAAC;wEAAE,WAAU;kFACV,OAAO,WAAW;;;;;;kFAErB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAe,OAAO,MAAM;;;;;;0FAC5C,8OAAC,kNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAtBjB;;;;;kDAgCnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,UAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;kFACf,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5B", "debugId": null}}, {"offset": {"line": 3638, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/footer.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { \n  Github, \n  Twitter, \n  Linkedin, \n  Mail, \n  ArrowRight,\n  Code,\n  FileText,\n  Users,\n  Building\n} from \"lucide-react\"\n\nexport function Footer() {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  }\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  }\n\n  const footerLinks = {\n    products: [\n      { name: \"Crefy Phygital\", href: \"#products\" },\n      { name: \"Crefy Connect\", href: \"#products\" },\n      { name: \"Enterprise\", href: \"#contact\" },\n      { name: \"Pricing\", href: \"#pricing\" }\n    ],\n    developers: [\n      { name: \"Documentation\", href: \"/docs\" },\n      { name: \"API Reference\", href: \"/docs/api\" },\n      { name: \"SDKs\", href: \"/docs/sdks\" },\n      { name: \"Examples\", href: \"/examples\" }\n    ],\n    company: [\n      { name: \"About\", href: \"/about\" },\n      { name: \"Blog\", href: \"/blog\" },\n      { name: \"Careers\", href: \"/careers\" },\n      { name: \"Contact\", href: \"#contact\" }\n    ],\n    legal: [\n      { name: \"Privacy Policy\", href: \"/privacy\" },\n      { name: \"Terms of Service\", href: \"/terms\" },\n      { name: \"Security\", href: \"/security\" },\n      { name: \"Status\", href: \"/status\" }\n    ]\n  }\n\n  const socialLinks = [\n    { icon: Twitter, href: \"https://twitter.com/crefy\", label: \"Twitter\" },\n    { icon: Github, href: \"https://github.com/crefy\", label: \"GitHub\" },\n    { icon: Linkedin, href: \"https://linkedin.com/company/crefy\", label: \"LinkedIn\" },\n    { icon: Mail, href: \"mailto:<EMAIL>\", label: \"Email\" }\n  ]\n\n  return (\n    <footer className=\"bg-white mx-4 mb-4 rounded-2xl glass-effect border border-purple-100/50 text-gray-900\">\n      {/* Newsletter Section */}\n      <motion.div\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true }}\n        variants={containerVariants}\n        className=\"border-b border-purple-100\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <motion.div \n            variants={itemVariants}\n            className=\"text-center max-w-2xl mx-auto\"\n          >\n            <h3 className=\"text-3xl font-bold mb-4\">\n              Stay Updated with CREFY\n            </h3>\n            <p className=\"text-gray-600 mb-8\">\n              Get the latest updates on new features, developer resources, and industry insights.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n              />\n              <Button className=\"bg-gradient-purple hover:opacity-90 text-white px-6\">\n                Subscribe\n                <ArrowRight className=\"ml-2 w-4 h-4\" />\n              </Button>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n\n      {/* Main Footer */}\n      <motion.div\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true }}\n        variants={containerVariants}\n        className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\"\n      >\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <motion.div variants={itemVariants} className=\"lg:col-span-2\">\n            <div className=\"text-3xl font-bold text-gradient mb-4\">CREFY</div>\n            <p className=\"text-gray-400 mb-6 max-w-sm\">\n              Foundational identity infrastructure for the Internet of Value. \n              Powering next-gen interactions between people, products, and platforms.\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social, index) => (\n                <motion.a\n                  key={index}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-purple-600 transition-colors duration-300\"\n                  aria-label={social.label}\n                >\n                  <social.icon className=\"w-5 h-5\" />\n                </motion.a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Products */}\n          <motion.div variants={itemVariants}>\n            <h4 className=\"font-semibold text-white mb-4 flex items-center\">\n              <Code className=\"w-4 h-4 mr-2\" />\n              Products\n            </h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.products.map((link, index) => (\n                <li key={index}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Developers */}\n          <motion.div variants={itemVariants}>\n            <h4 className=\"font-semibold text-white mb-4 flex items-center\">\n              <FileText className=\"w-4 h-4 mr-2\" />\n              Developers\n            </h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.developers.map((link, index) => (\n                <li key={index}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Company */}\n          <motion.div variants={itemVariants}>\n            <h4 className=\"font-semibold text-white mb-4 flex items-center\">\n              <Building className=\"w-4 h-4 mr-2\" />\n              Company\n            </h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link, index) => (\n                <li key={index}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Legal */}\n          <motion.div variants={itemVariants}>\n            <h4 className=\"font-semibold text-white mb-4 flex items-center\">\n              <Users className=\"w-4 h-4 mr-2\" />\n              Legal\n            </h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.legal.map((link, index) => (\n                <li key={index}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n        </div>\n      </motion.div>\n\n      {/* Bottom Bar */}\n      <motion.div\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true }}\n        variants={itemVariants}\n        className=\"border-t border-gray-800\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-400 text-sm\">\n              © 2025 CREFY. All rights reserved.\n            </p>\n            <div className=\"flex items-center space-x-6 mt-4 md:mt-0\">\n              <span className=\"text-gray-400 text-sm\">Built with ❤️ for developers</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-gray-400 text-sm\">All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAgBO,SAAS;IACd,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,cAAc;QAClB,UAAU;YACR;gBAAE,MAAM;gBAAkB,MAAM;YAAY;YAC5C;gBAAE,MAAM;gBAAiB,MAAM;YAAY;YAC3C;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,YAAY;YACV;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAiB,MAAM;YAAY;YAC3C;gBAAE,MAAM;gBAAQ,MAAM;YAAa;YACnC;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAA6B,OAAO;QAAU;QACrE;YAAE,MAAM,sMAAA,CAAA,SAAM;YAAE,MAAM;YAA4B,OAAO;QAAS;QAClE;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAsC,OAAO;QAAW;QAChF;YAAE,MAAM,kMAAA,CAAA,OAAI;YAAE,MAAM;YAA0B,OAAO;QAAQ;KAC9D;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;gBAAK;gBACvB,UAAU;gBACV,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;4CAAsD;0DAEtE,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;gBAAK;gBACvB,UAAU;gBACV,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CAEP,MAAM,OAAO,IAAI;4CACjB,QAAO;4CACP,KAAI;4CACJ,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;4CACV,cAAY,OAAO,KAAK;sDAExB,cAAA,8OAAC,OAAO,IAAI;gDAAC,WAAU;;;;;;2CATlB;;;;;;;;;;;;;;;;sCAgBb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;;8CACpB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;sCAaf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;;8CACpB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC;oCAAG,WAAU;8CACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;sCAaf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;;8CACpB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;sCAaf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;;8CACpB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAenB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;gBAAK;gBACvB,UAAU;gBACV,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxD", "debugId": null}}]}