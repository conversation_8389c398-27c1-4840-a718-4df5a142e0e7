{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n\nexport function scrollToSection(sectionId: string) {\n  const element = document.getElementById(sectionId)\n  if (element) {\n    element.scrollIntoView({ behavior: 'smooth' })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAEO,SAAS,gBAAgB,SAAiB;IAC/C,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,QAAQ,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9C;AACF", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/navigation/navbar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { motion } from \"framer-motion\"\nimport { Button } from \"@/components/ui/button\"\nimport { Menu, X, Code, FileText, Users, Mail } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nexport function Navbar() {\n  const [isScrolled, setIsScrolled] = useState(false)\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50)\n    }\n    window.addEventListener(\"scroll\", handleScroll)\n    return () => window.removeEventListener(\"scroll\", handleScroll)\n  }, [])\n\n  const navItems = [\n    { name: \"Products\", href: \"#products\", icon: Code },\n    { name: \"Features\", href: \"#features\", icon: FileText },\n    { name: \"Developers\", href: \"#developers\", icon: Users },\n    { name: \"Contact\", href: \"#contact\", icon: Mail },\n  ]\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href)\n    if (element) {\n      element.scrollIntoView({ behavior: \"smooth\" })\n    }\n    setIsMobileMenuOpen(false)\n  }\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6 }}\n      className={cn(\n        \"fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl\",\n        isScrolled\n          ? \"glass-effect-strong shadow-lg border border-purple-100/50\"\n          : \"glass-effect border border-white/30\"\n      )}\n    >\n      <div className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-10\">\n        <div className=\"flex justify-between items-center h-12\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center\"\n          >\n            <div className=\"text-xl font-bold text-gradient cursor-pointer hover-scale\">\n              CREFY\n            </div>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-6\">\n            {navItems.map((item) => (\n              <motion.button\n                key={item.name}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => scrollToSection(item.href)}\n                className=\"flex items-center space-x-1 text-black hover:text-purple-600 transition-colors duration-200 font-medium text-sm\"\n              >\n                <item.icon className=\"w-3.5 h-3.5\" />\n                <span>{item.name}</span>\n              </motion.button>\n            ))}\n          </div>\n\n\n          {/* Mobile Menu Button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"text-gray-700\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden border-t border-purple-100 bg-white/95 backdrop-blur-md\"\n          >\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navItems.map((item) => (\n                <button\n                  key={item.name}\n                  onClick={() => scrollToSection(item.href)}\n                  className=\"flex items-center space-x-2 w-full text-left px-3 py-2 text-gray-700 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors duration-200\"\n                >\n                  <item.icon className=\"w-4 h-4\" />\n                  <span>{item.name}</span>\n                </button>\n              ))}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </motion.nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,qMAAA,CAAA,OAAI;QAAC;QAClD;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,iNAAA,CAAA,WAAQ;QAAC;QACtD;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,uMAAA,CAAA,QAAK;QAAC;QACvD;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,qMAAA,CAAA,OAAI;QAAC;KACjD;IAED,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,oBAAoB;IACtB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2EACA,aACI,8DACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;0CAA6D;;;;;;;;;;;sCAM9E,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,gBAAgB,KAAK,IAAI;oCACxC,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;mCAPX,KAAK,IAAI;;;;;;;;;;sCAcpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAET,iCACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAOvB,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;gCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;gCACxC,WAAU;;kDAEV,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAM,KAAK,IAAI;;;;;;;+BALX,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC;GA/GgB;KAAA", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/hooks/use-scroll-animations.ts"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useRef } from \"react\"\nimport { useScroll, useTransform, useSpring } from \"framer-motion\"\n\nexport function useScrollAnimations() {\n  const { scrollY } = useScroll()\n  \n  // Parallax transforms for different speeds\n  const parallaxSlow = useTransform(scrollY, [0, 1000], [0, -100])\n  const parallaxMedium = useTransform(scrollY, [0, 1000], [0, -200])\n  const parallaxFast = useTransform(scrollY, [0, 1000], [0, -300])\n  \n  // Smooth spring animations\n  const smoothParallaxSlow = useSpring(parallaxSlow, { stiffness: 100, damping: 30 })\n  const smoothParallaxMedium = useSpring(parallaxMedium, { stiffness: 100, damping: 30 })\n  const smoothParallaxFast = useSpring(parallaxFast, { stiffness: 100, damping: 30 })\n  \n  return {\n    parallaxSlow: smoothParallaxSlow,\n    parallaxMedium: smoothParallaxMedium,\n    parallaxFast: smoothParallaxFast,\n    scrollY\n  }\n}\n\nexport function useScrollReveal() {\n  const ref = useRef<HTMLElement>(null)\n  \n  useEffect(() => {\n    const element = ref.current\n    if (!element) return\n    \n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            entry.target.classList.add('revealed')\n          }\n        })\n      },\n      {\n        threshold: 0.1,\n        rootMargin: '0px 0px -50px 0px'\n      }\n    )\n    \n    // Find all scroll-reveal elements within this component\n    const scrollElements = element.querySelectorAll('.scroll-reveal')\n    scrollElements.forEach((el) => observer.observe(el))\n    \n    return () => {\n      scrollElements.forEach((el) => observer.unobserve(el))\n    }\n  }, [])\n  \n  return ref\n}\n\n// Utility for staggered animations\nexport const staggeredRevealVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.1,\n      delayChildren: 0.2\n    }\n  }\n}\n\nexport const revealItemVariants = {\n  hidden: { \n    opacity: 0, \n    y: 30,\n    scale: 0.95\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    scale: 1,\n    transition: {\n      duration: 0.6,\n      ease: [0.4, 0, 0.2, 1]\n    }\n  }\n}\n\n// Enhanced hover variants\nexport const enhancedHoverVariants = {\n  initial: { scale: 1, y: 0 },\n  hover: { \n    scale: 1.02, \n    y: -4,\n    transition: {\n      duration: 0.2,\n      ease: \"easeOut\"\n    }\n  },\n  tap: { \n    scale: 0.98,\n    transition: {\n      duration: 0.1\n    }\n  }\n}\n\n// Floating animation variants\nexport const floatingVariants = {\n  animate: {\n    y: [-10, 10, -10],\n    transition: {\n      duration: 6,\n      repeat: Infinity,\n      ease: \"easeInOut\"\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAAA;AAAA;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;IAE5B,2CAA2C;IAC3C,MAAM,eAAe,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,SAAS;QAAC;QAAG;KAAK,EAAE;QAAC;QAAG,CAAC;KAAI;IAC/D,MAAM,iBAAiB,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,SAAS;QAAC;QAAG;KAAK,EAAE;QAAC;QAAG,CAAC;KAAI;IACjE,MAAM,eAAe,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,SAAS;QAAC;QAAG;KAAK,EAAE;QAAC;QAAG,CAAC;KAAI;IAE/D,2BAA2B;IAC3B,MAAM,qBAAqB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,WAAW;QAAK,SAAS;IAAG;IACjF,MAAM,uBAAuB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;QAAE,WAAW;QAAK,SAAS;IAAG;IACrF,MAAM,qBAAqB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE,WAAW;QAAK,SAAS;IAAG;IAEjF,OAAO;QACL,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd;IACF;AACF;GAnBgB;;QACM,4KAAA,CAAA,YAAS;QAGR,+KAAA,CAAA,eAAY;QACV,+KAAA,CAAA,eAAY;QACd,+KAAA,CAAA,eAAY;QAGN,4KAAA,CAAA,YAAS;QACP,4KAAA,CAAA,YAAS;QACX,4KAAA,CAAA,YAAS;;;AAU/B,SAAS;;IACd,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,UAAU,IAAI,OAAO;YAC3B,IAAI,CAAC,SAAS;YAEd,MAAM,WAAW,IAAI;6CACnB,CAAC;oBACC,QAAQ,OAAO;qDAAC,CAAC;4BACf,IAAI,MAAM,cAAc,EAAE;gCACxB,MAAM,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;4BAC7B;wBACF;;gBACF;4CACA;gBACE,WAAW;gBACX,YAAY;YACd;YAGF,wDAAwD;YACxD,MAAM,iBAAiB,QAAQ,gBAAgB,CAAC;YAChD,eAAe,OAAO;6CAAC,CAAC,KAAO,SAAS,OAAO,CAAC;;YAEhD;6CAAO;oBACL,eAAe,OAAO;qDAAC,CAAC,KAAO,SAAS,SAAS,CAAC;;gBACpD;;QACF;oCAAG,EAAE;IAEL,OAAO;AACT;IA/BgB;AAkCT,MAAM,0BAA0B;IACrC,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;YACjB,eAAe;QACjB;IACF;AACF;AAEO,MAAM,qBAAqB;IAChC,QAAQ;QACN,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;QACP,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK;gBAAG;gBAAK;aAAE;QACxB;IACF;AACF;AAGO,MAAM,wBAAwB;IACnC,SAAS;QAAE,OAAO;QAAG,GAAG;IAAE;IAC1B,OAAO;QACL,OAAO;QACP,GAAG,CAAC;QACJ,YAAY;YACV,UAAU;YACV,MAAM;QACR;IACF;IACA,KAAK;QACH,OAAO;QACP,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,SAAS;QACP,GAAG;YAAC,CAAC;YAAI;YAAI,CAAC;SAAG;QACjB,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/ui/floating-orbs.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from \"react\"\nimport { motion } from \"framer-motion\"\nimport { useScrollAnimations } from \"@/hooks/use-scroll-animations\"\n\ninterface Orb {\n  id: number\n  size: number\n  x: number\n  y: number\n  color: string\n  animationClass: string\n  opacity: number\n  delay: number\n  parallaxSpeed: 'slow' | 'medium' | 'fast'\n  shape: 'circle' | 'ellipse' | 'blob'\n  sizeCategory: 'small' | 'medium' | 'large' | 'extra-large'\n}\n\ninterface FloatingOrbsProps {\n  count?: number\n  className?: string\n  enableShapes?: boolean\n}\n\nexport function FloatingOrbs({ count = 8, className = \"\", enableShapes = true }: FloatingOrbsProps) {\n  const { parallaxSlow, parallaxMedium, parallaxFast } = useScrollAnimations()\n  const [orbs, setOrbs] = useState<Orb[]>([])\n\n  useEffect(() => {\n    const colors = [\n      'rgba(107, 33, 168, 0.25)',   // deep-purple\n      'rgba(67, 56, 202, 0.25)',    // indigo\n      'rgba(124, 58, 237, 0.25)',   // royal-purple\n      'rgba(107, 33, 168, 0.15)',   // deep-purple lighter\n      'rgba(67, 56, 202, 0.15)',    // indigo lighter\n      'rgba(124, 58, 237, 0.15)',   // royal-purple lighter\n    ]\n\n    const animationClasses = [\n      'floating-orb-1',\n      'floating-orb-2',\n      'floating-orb-3',\n      'floating-orb-4',\n      'floating-orb-5'\n    ]\n\n    const parallaxSpeeds: ('slow' | 'medium' | 'fast')[] = ['slow', 'medium', 'fast']\n    const shapes: ('circle' | 'ellipse' | 'blob')[] = ['circle', 'ellipse', 'blob']\n    const sizeCategories: ('small' | 'medium' | 'large' | 'extra-large')[] = ['small', 'medium', 'large', 'extra-large']\n\n    const getSizeFromCategory = (category: 'small' | 'medium' | 'large' | 'extra-large') => {\n      switch (category) {\n        case 'small': return Math.random() * 80 + 40 // 40-120px\n        case 'medium': return Math.random() * 120 + 100 // 100-220px\n        case 'large': return Math.random() * 180 + 200 // 200-380px\n        case 'extra-large': return Math.random() * 250 + 300 // 300-550px\n        default: return Math.random() * 120 + 100\n      }\n    }\n\n    const generateOrbs = () => {\n      const newOrbs: Orb[] = []\n\n      for (let i = 0; i < count; i++) {\n        const sizeCategory = sizeCategories[Math.floor(Math.random() * sizeCategories.length)]\n        newOrbs.push({\n          id: i,\n          size: getSizeFromCategory(sizeCategory),\n          x: Math.random() * 120 - 10, // -10% to 110% for edge overflow\n          y: Math.random() * 120 - 10, // -10% to 110% for edge overflow\n          color: colors[Math.floor(Math.random() * colors.length)],\n          animationClass: animationClasses[Math.floor(Math.random() * animationClasses.length)],\n          opacity: Math.random() * 0.3 + 0.1, // 0.1 to 0.4\n          delay: Math.random() * 8, // 0 to 8 seconds delay\n          parallaxSpeed: parallaxSpeeds[Math.floor(Math.random() * parallaxSpeeds.length)],\n          shape: enableShapes ? shapes[Math.floor(Math.random() * shapes.length)] : 'circle',\n          sizeCategory,\n        })\n      }\n\n      setOrbs(newOrbs)\n    }\n\n    generateOrbs()\n  }, [count])\n\n  const getParallaxTransform = (speed: 'slow' | 'medium' | 'fast') => {\n    switch (speed) {\n      case 'slow': return parallaxSlow\n      case 'medium': return parallaxMedium\n      case 'fast': return parallaxFast\n      default: return parallaxSlow\n    }\n  }\n\n  const getShapeStyles = (shape: 'circle' | 'ellipse' | 'blob', size: number) => {\n    const baseStyles = {\n      width: `${size}px`,\n      height: `${size}px`,\n    }\n\n    switch (shape) {\n      case 'circle':\n        return { ...baseStyles, borderRadius: '50%' }\n      case 'ellipse':\n        return {\n          ...baseStyles,\n          width: `${size * 1.4}px`,\n          height: `${size * 0.7}px`,\n          borderRadius: '50%'\n        }\n      case 'blob':\n        return {\n          ...baseStyles,\n          borderRadius: `${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}%`\n        }\n      default:\n        return { ...baseStyles, borderRadius: '50%' }\n    }\n  }\n\n  return (\n    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>\n      {orbs.map((orb) => (\n        <motion.div\n          key={orb.id}\n          className={`floating-orb ${orb.animationClass}`}\n          style={{\n            ...getShapeStyles(orb.shape, orb.size),\n            left: `${orb.x}%`,\n            top: `${orb.y}%`,\n            backgroundColor: orb.color,\n            opacity: orb.opacity,\n            animationDelay: `${orb.delay}s`,\n            transform: `translate(-50%, -50%)`,\n            y: getParallaxTransform(orb.parallaxSpeed),\n          }}\n        />\n      ))}\n\n      {/* Additional gradient overlay for depth */}\n      <motion.div\n        className=\"absolute inset-0 bg-gradient-to-br from-transparent via-white/3 to-transparent pointer-events-none\"\n        style={{ y: parallaxSlow }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AA0BO,SAAS,aAAa,EAAE,QAAQ,CAAC,EAAE,YAAY,EAAE,EAAE,eAAe,IAAI,EAAqB;;IAChG,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;IACzE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,mBAAmB;gBACvB;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,iBAAiD;gBAAC;gBAAQ;gBAAU;aAAO;YACjF,MAAM,SAA4C;gBAAC;gBAAU;gBAAW;aAAO;YAC/E,MAAM,iBAAmE;gBAAC;gBAAS;gBAAU;gBAAS;aAAc;YAEpH,MAAM;8DAAsB,CAAC;oBAC3B,OAAQ;wBACN,KAAK;4BAAS,OAAO,KAAK,MAAM,KAAK,KAAK,GAAG,WAAW;;wBACxD,KAAK;4BAAU,OAAO,KAAK,MAAM,KAAK,MAAM,IAAI,YAAY;;wBAC5D,KAAK;4BAAS,OAAO,KAAK,MAAM,KAAK,MAAM,IAAI,YAAY;;wBAC3D,KAAK;4BAAe,OAAO,KAAK,MAAM,KAAK,MAAM,IAAI,YAAY;;wBACjE;4BAAS,OAAO,KAAK,MAAM,KAAK,MAAM;oBACxC;gBACF;;YAEA,MAAM;uDAAe;oBACnB,MAAM,UAAiB,EAAE;oBAEzB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;wBAC9B,MAAM,eAAe,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;wBACtF,QAAQ,IAAI,CAAC;4BACX,IAAI;4BACJ,MAAM,oBAAoB;4BAC1B,GAAG,KAAK,MAAM,KAAK,MAAM;4BACzB,GAAG,KAAK,MAAM,KAAK,MAAM;4BACzB,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;4BACxD,gBAAgB,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;4BACrF,SAAS,KAAK,MAAM,KAAK,MAAM;4BAC/B,OAAO,KAAK,MAAM,KAAK;4BACvB,eAAe,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;4BAChF,OAAO,eAAe,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE,GAAG;4BAC1E;wBACF;oBACF;oBAEA,QAAQ;gBACV;;YAEA;QACF;iCAAG;QAAC;KAAM;IAEV,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC,OAAsC;QAC5D,MAAM,aAAa;YACjB,OAAO,GAAG,KAAK,EAAE,CAAC;YAClB,QAAQ,GAAG,KAAK,EAAE,CAAC;QACrB;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,GAAG,UAAU;oBAAE,cAAc;gBAAM;YAC9C,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;oBACxB,QAAQ,GAAG,OAAO,IAAI,EAAE,CAAC;oBACzB,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,cAAc,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,EAAE,EAAE,KAAK,MAAM,KAAK,KAAK,GAAG,EAAE,EAAE,KAAK,MAAM,KAAK,KAAK,GAAG,EAAE,EAAE,KAAK,MAAM,KAAK,KAAK,GAAG,CAAC,CAAC;gBACjI;YACF;gBACE,OAAO;oBAAE,GAAG,UAAU;oBAAE,cAAc;gBAAM;QAChD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;YAChF,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAW,CAAC,aAAa,EAAE,IAAI,cAAc,EAAE;oBAC/C,OAAO;wBACL,GAAG,eAAe,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC;wBACtC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBACjB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBAChB,iBAAiB,IAAI,KAAK;wBAC1B,SAAS,IAAI,OAAO;wBACpB,gBAAgB,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;wBAC/B,WAAW,CAAC,qBAAqB,CAAC;wBAClC,GAAG,qBAAqB,IAAI,aAAa;oBAC3C;mBAXK,IAAI,EAAE;;;;;0BAgBf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,GAAG;gBAAa;;;;;;;;;;;;AAIjC;GA3HgB;;QACyC,8IAAA,CAAA,sBAAmB;;;KAD5D", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/products.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  Smartphone,\n  QrCode,\n  Shield,\n  Users,\n  Wallet,\n  Globe,\n  ArrowRight,\n  Zap,\n  Database,\n  BarChart3\n} from \"lucide-react\"\n\nexport function ProductsSection() {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3\n      }\n    }\n  }\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  }\n\n  return (\n    <section id=\"products\" className=\"relative py-24 bg-white overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-black mb-6\"\n          >\n            Two Powerful Products,\n            <br />\n            <span className=\"text-gradient\">One Identity Platform</span>\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-black max-w-3xl mx-auto\"\n          >\n            CREFY's modular approach delivers specialized solutions for physical product tokenization \n            and seamless Web3 onboarding, built on a unified identity infrastructure.\n          </motion.p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\n          {/* Crefy Phygital */}\n          <motion.div\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true }}\n            variants={itemVariants}\n          >\n            <Card className=\"h-full glass-effect border-2 border-purple-100 hover:border-purple-200 hover-lift hover-glow scroll-reveal\">\n              <CardHeader className=\"text-center pb-8\">\n                <div className=\"w-16 h-16 bg-gradient-purple rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <QrCode className=\"w-8 h-8 text-white\" />\n                </div>\n                <CardTitle className=\"text-3xl font-bold text-black\">\n                  Crefy Phygital\n                </CardTitle>\n                <CardDescription className=\"text-lg text-black\">\n                  Transform physical products into verifiable digital assets with seamless tokenization and lifecycle management.\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-6\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div className=\"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\">\n                    <Smartphone className=\"w-5 h-5 text-purple-600\" />\n                    <span className=\"text-sm font-medium\">Mobile Scanner</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\">\n                    <Shield className=\"w-5 h-5 text-purple-600\" />\n                    <span className=\"text-sm font-medium\">Anti-Counterfeit</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\">\n                    <Database className=\"w-5 h-5 text-purple-600\" />\n                    <span className=\"text-sm font-medium\">Asset Tracking</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\">\n                    <BarChart3 className=\"w-5 h-5 text-purple-600\" />\n                    <span className=\"text-sm font-medium\">Analytics</span>\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <h4 className=\"font-semibold text-gray-900\">Key Features:</h4>\n                  <ul className=\"space-y-2 text-gray-600\">\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>RESTful APIs for token creation, minting, and redemption</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>NFC/QR scanner apps for iOS and Android</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>Company dashboard for asset management</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>User portal for claiming and viewing assets</span>\n                    </li>\n                  </ul>\n                </div>\n\n                <Button className=\"w-full bg-gradient-purple text-white btn-enhanced hover-lift micro-bounce\">\n                  Explore Phygital\n                  <ArrowRight className=\"ml-2 w-4 h-4\" />\n                </Button>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Crefy Connect */}\n          <motion.div\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true }}\n            variants={itemVariants}\n          >\n            <Card className=\"h-full glass-effect border-2 border-indigo-100 hover:border-indigo-200 hover-lift hover-glow scroll-reveal\">\n              <CardHeader className=\"text-center pb-8\">\n                <div className=\"w-16 h-16 bg-gradient-purple rounded-2xl flex items-center justify-center mx-auto mb-4 hover-scale\">\n                  <Wallet className=\"w-8 h-8 text-white\" />\n                </div>\n                <CardTitle className=\"text-3xl font-bold text-black\">\n                  Crefy Connect\n                </CardTitle>\n                <CardDescription className=\"text-lg text-black\">\n                  Seamless social login with embedded smart wallets that abstract away Web3 complexity for users.\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-6\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div className=\"flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg\">\n                    <Users className=\"w-5 h-5 text-indigo-600\" />\n                    <span className=\"text-sm font-medium\">Social Auth</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg\">\n                    <Zap className=\"w-5 h-5 text-indigo-600\" />\n                    <span className=\"text-sm font-medium\">Smart Wallets</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg\">\n                    <Globe className=\"w-5 h-5 text-indigo-600\" />\n                    <span className=\"text-sm font-medium\">ENS Integration</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg\">\n                    <BarChart3 className=\"w-5 h-5 text-indigo-600\" />\n                    <span className=\"text-sm font-medium\">Dev Analytics</span>\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <h4 className=\"font-semibold text-gray-900\">Supported Login Methods:</h4>\n                  <div className=\"grid grid-cols-3 gap-2 text-sm\">\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">Twitter</div>\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">Google</div>\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">Email</div>\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">Phone</div>\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">Discord</div>\n                    <div className=\"bg-gray-50 p-2 rounded text-center\">EOA</div>\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <h4 className=\"font-semibold text-gray-900\">Key Features:</h4>\n                  <ul className=\"space-y-2 text-gray-600\">\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>Account Abstraction with smart contract wallets</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>Auto-assigned ENS names and subdomains</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>Integrated fiat on/off ramps</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0\"></div>\n                      <span>Developer analytics and monitoring</span>\n                    </li>\n                  </ul>\n                </div>\n\n                <Button className=\"w-full bg-gradient-purple text-white btn-enhanced hover-lift micro-bounce\">\n                  Explore Connect\n                  <ArrowRight className=\"ml-2 w-4 h-4\" />\n                </Button>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAkBO,SAAS;IACd,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;;gCACX;8CAEC,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAElC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAMH,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;4BAAK;4BACvB,UAAU;sCAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAgC;;;;;;0DAGrD,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAqB;;;;;;;;;;;;kDAIlD,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;;;;;;;0DAI1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;0DAKZ,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;oDAA4E;kEAE5F,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;4BAAK;4BACvB,UAAU;sCAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAgC;;;;;;0DAGrD,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAqB;;;;;;;;;;;;kDAIlD,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;;;;;;;0DAI1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAqC;;;;;;0EACpD,6LAAC;gEAAI,WAAU;0EAAqC;;;;;;0EACpD,6LAAC;gEAAI,WAAU;0EAAqC;;;;;;0EACpD,6LAAC;gEAAI,WAAU;0EAAqC;;;;;;0EACpD,6LAAC;gEAAI,WAAU;0EAAqC;;;;;;0EACpD,6LAAC;gEAAI,WAAU;0EAAqC;;;;;;;;;;;;;;;;;;0DAIxD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;0DAKZ,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;oDAA4E;kEAE5F,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;KAzMgB", "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/features.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport {\n  Smartphone,\n  QrCode,\n  Shield,\n  Wallet,\n  Globe,\n  Zap,\n  Database,\n  BarChart3,\n  Code,\n  Lock,\n  Users,\n  ArrowUpRight\n} from \"lucide-react\"\n\nexport function FeaturesSection() {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  }\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  }\n\n  const features = [\n    {\n      icon: QrCode,\n      title: \"NFC/QR Scanning\",\n      description: \"Advanced scanning technology for instant product verification and redemption with mobile apps for iOS and Android.\",\n      color: \"purple\",\n      stats: \"99.9% accuracy\"\n    },\n    {\n      icon: Wallet,\n      title: \"Smart Wallet Creation\",\n      description: \"Automatic smart contract wallet generation using Account Abstraction, eliminating seed phrase complexity.\",\n      color: \"indigo\",\n      stats: \"Sub-second creation\"\n    },\n    {\n      icon: Globe,\n      title: \"ENS Integration\",\n      description: \"Auto-assigned ENS names and customizable subdomains for seamless Web3 identity management.\",\n      color: \"purple\",\n      stats: \"Custom domains\"\n    },\n    {\n      icon: Code,\n      title: \"Developer APIs\",\n      description: \"RESTful APIs with comprehensive documentation, SDKs, and examples for rapid integration.\",\n      color: \"indigo\",\n      stats: \"99.9% uptime\"\n    },\n    {\n      icon: Shield,\n      title: \"Anti-Counterfeit Protection\",\n      description: \"Blockchain-based verification system that makes product counterfeiting virtually impossible.\",\n      color: \"purple\",\n      stats: \"Zero false positives\"\n    },\n    {\n      icon: BarChart3,\n      title: \"Real-time Analytics\",\n      description: \"Comprehensive dashboards with insights on user behavior, token activity, and system performance.\",\n      color: \"indigo\",\n      stats: \"Live monitoring\"\n    }\n  ]\n\n  const capabilities = [\n    {\n      title: \"Multi-Chain Support\",\n      description: \"Deploy across Ethereum, Polygon, and other EVM-compatible networks\",\n      icon: Database\n    },\n    {\n      title: \"Social Login Integration\",\n      description: \"Support for 6+ authentication methods including Twitter, Google, and Discord\",\n      icon: Users\n    },\n    {\n      title: \"Enterprise Security\",\n      description: \"Bank-grade security with SOC 2 compliance and end-to-end encryption\",\n      icon: Lock\n    },\n    {\n      title: \"Scalable Infrastructure\",\n      description: \"Auto-scaling architecture supporting millions of users and transactions\",\n      icon: Zap\n    }\n  ]\n\n  return (\n    <section id=\"features\" className=\"relative py-24 bg-white overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-black mb-6\"\n          >\n            Powerful Features for\n            <br />\n            <span className=\"text-gradient\">Modern Identity Infrastructure</span>\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-black max-w-3xl mx-auto\"\n          >\n            Built with cutting-edge technology to deliver seamless experiences for both developers and end users.\n          </motion.p>\n        </motion.div>\n\n        {/* Main Features Grid */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\"\n        >\n          {features.map((feature, index) => (\n            <motion.div key={index} variants={itemVariants}>\n              <Card className=\"h-full glass-effect border-2 border-gray-100 hover:border-purple-200 hover-lift hover-glow scroll-reveal group\">\n                <CardHeader>\n                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center mb-4 hover-scale ${\n                    feature.color === 'purple'\n                      ? 'bg-purple-100 group-hover:bg-purple-200'\n                      : 'bg-indigo-100 group-hover:bg-indigo-200'\n                  } transition-colors duration-300`}>\n                    <feature.icon className={`w-6 h-6 ${\n                      feature.color === 'purple' ? 'text-purple-600' : 'text-indigo-600'\n                    }`} />\n                  </div>\n                  <CardTitle className=\"text-xl font-bold text-black\">\n                    {feature.title}\n                  </CardTitle>\n                  <CardDescription className=\"text-black\">\n                    {feature.description}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"flex items-center justify-between\">\n                    <span className={`text-sm font-semibold ${\n                      feature.color === 'purple' ? 'text-purple-600' : 'text-indigo-600'\n                    }`}>\n                      {feature.stats}\n                    </span>\n                    <ArrowUpRight className=\"w-4 h-4 text-gray-400 group-hover:text-purple-600 transition-colors duration-300\" />\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Additional Capabilities */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-3xl p-8 md:p-12\"\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-12\">\n            <h3 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Enterprise-Grade Capabilities\n            </h3>\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n              Built to scale with your business, from startup to enterprise, with the reliability and security you need.\n            </p>\n          </motion.div>\n\n          <motion.div\n            variants={containerVariants}\n            className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\"\n          >\n            {capabilities.map((capability, index) => (\n              <motion.div\n                key={index}\n                variants={itemVariants}\n                className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/50 hover:bg-white transition-all duration-300\"\n              >\n                <div className=\"w-10 h-10 bg-gradient-purple rounded-lg flex items-center justify-center mb-4\">\n                  <capability.icon className=\"w-5 h-5 text-white\" />\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">\n                  {capability.title}\n                </h4>\n                <p className=\"text-sm text-gray-600\">\n                  {capability.description}\n                </p>\n              </motion.div>\n            ))}\n          </motion.div>\n        </motion.div>\n\n        {/* Performance Stats */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center\"\n        >\n          {[\n            { value: \"99.9%\", label: \"Uptime SLA\" },\n            { value: \"<100ms\", label: \"API Response\" },\n            { value: \"50M+\", label: \"API Calls/Month\" },\n            { value: \"24/7\", label: \"Support\" }\n          ].map((stat, index) => (\n            <motion.div key={index} variants={itemVariants}>\n              <div className=\"text-3xl md:text-4xl font-bold text-gradient mb-2\">\n                {stat.value}\n              </div>\n              <div className=\"text-gray-600 font-medium\">\n                {stat.label}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAmBO,SAAS;IACd,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,WAAW;QACf;YACE,MAAM,6MAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,qNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,eAAe;QACnB;YACE,OAAO;YACP,aAAa;YACb,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,uMAAA,CAAA,QAAK;QACb;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,mMAAA,CAAA,MAAG;QACX;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;;gCACX;8CAEC,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAElC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;8BAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAa,UAAU;sCAChC,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC;gDAAI,WAAW,CAAC,uEAAuE,EACtF,QAAQ,KAAK,KAAK,WACd,4CACA,0CACL,+BAA+B,CAAC;0DAC/B,cAAA,6LAAC,QAAQ,IAAI;oDAAC,WAAW,CAAC,QAAQ,EAChC,QAAQ,KAAK,KAAK,WAAW,oBAAoB,mBACjD;;;;;;;;;;;0DAEJ,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,QAAQ,KAAK;;;;;;0DAEhB,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;kDAGxB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAW,CAAC,sBAAsB,EACtC,QAAQ,KAAK,KAAK,WAAW,oBAAoB,mBACjD;8DACC,QAAQ,KAAK;;;;;;8DAEhB,6LAAC,6NAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2BA1Bf;;;;;;;;;;8BAmCrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAET,aAAa,GAAG,CAAC,CAAC,YAAY,sBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,WAAW,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAE7B,6LAAC;4CAAG,WAAU;sDACX,WAAW,KAAK;;;;;;sDAEnB,6LAAC;4CAAE,WAAU;sDACV,WAAW,WAAW;;;;;;;mCAXpB;;;;;;;;;;;;;;;;8BAmBb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;8BAET;wBACC;4BAAE,OAAO;4BAAS,OAAO;wBAAa;wBACtC;4BAAE,OAAO;4BAAU,OAAO;wBAAe;wBACzC;4BAAE,OAAO;4BAAQ,OAAO;wBAAkB;wBAC1C;4BAAE,OAAO;4BAAQ,OAAO;wBAAU;qBACnC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAa,UAAU;;8CAChC,6LAAC;oCAAI,WAAU;8CACZ,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,KAAK,KAAK;;;;;;;2BALE;;;;;;;;;;;;;;;;;;;;;AAa7B;KAlOgB", "debugId": null}}, {"offset": {"line": 2098, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/developers.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  Code,\n  Terminal,\n  Book,\n  Zap,\n  Copy,\n  Check,\n  ArrowRight,\n  Github,\n  FileText,\n  Play\n} from \"lucide-react\"\n\nexport function DevelopersSection() {\n  const [copiedCode, setCopiedCode] = useState<string | null>(null)\n\n  const copyToClipboard = (code: string, id: string) => {\n    navigator.clipboard.writeText(code)\n    setCopiedCode(id)\n    setTimeout(() => setCopiedCode(null), 2000)\n  }\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  }\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  }\n\n  const connectCode = `// Initialize Crefy Connect Authentication\nimport { CrefyConnect } from '@crefy/connect-sdk'\n\nconst crefy = new CrefyConnect({\n  apiKey: 'your-api-key',\n  environment: 'production'\n})\n\n// Configure social login providers\nconst authConfig = {\n  providers: ['google', 'twitter', 'discord'],\n  walletOptions: {\n    autoCreate: true,\n    network: 'ethereum'\n  }\n}\n\n// Authenticate user with social login\nconst user = await crefy.auth.login({\n  provider: 'google',\n  config: authConfig\n})\n\n// Access embedded wallet\nconst wallet = user.wallet\nconsole.log(\\`User wallet: \\${wallet.address}\\`)\nconsole.log(\\`Balance: \\${await wallet.getBalance()}\\`)`\n\n  const connectCode = `// Initialize Crefy Connect SDK\nimport { CrefyConnect } from '@crefy/connect-sdk'\n\nconst connect = new CrefyConnect({\n  projectId: 'your-project-id',\n  chains: ['ethereum', 'polygon'],\n  ensSubdomain: 'yourapp.crefy.eth'\n})\n\n// Social login with embedded wallet\nconst user = await connect.auth.login({\n  provider: 'google', // twitter, discord, email, phone\n  redirectUri: 'https://yourapp.com/callback'\n})\n\n// Access user's smart wallet\nconst wallet = user.wallet\nconst balance = await wallet.getBalance()\nconst address = wallet.getAddress()\n\n// Send transaction (gasless with AA)\nconst tx = await wallet.sendTransaction({\n  to: '0x...',\n  value: '0.1',\n  data: '0x...'\n})\n\nconsole.log(\\`Transaction sent: \\${tx.hash}\\`)`\n\n  const resources = [\n    {\n      icon: Book,\n      title: \"Connect API Documentation\",\n      description: \"Complete guides for social login and wallet integration\",\n      link: \"/docs/connect\",\n      color: \"purple\"\n    },\n    {\n      icon: Code,\n      title: \"Connect SDK\",\n      description: \"JavaScript and React SDKs for seamless Web3 onboarding\",\n      link: \"/docs/connect-sdk\",\n      color: \"indigo\"\n    },\n    {\n      icon: Terminal,\n      title: \"Developer Tools\",\n      description: \"CLI and testing tools for Connect integration\",\n      link: \"/docs/tools\",\n      color: \"purple\"\n    },\n    {\n      icon: Github,\n      title: \"Connect Examples\",\n      description: \"Open-source examples and starter templates for Connect\",\n      link: \"/examples/connect\",\n      color: \"indigo\"\n    }\n  ]\n\n  return (\n    <section id=\"developers\" className=\"relative py-24 bg-white overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\"\n          >\n            Built for\n            <br />\n            <span className=\"text-gradient\">Developers</span>\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto\"\n          >\n            Get started in minutes with our developer-first APIs, comprehensive SDKs, \n            and extensive documentation. Build the future of identity infrastructure.\n          </motion.p>\n        </motion.div>\n\n        {/* Code Examples */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"mb-20\"\n        >\n          <motion.div variants={itemVariants} className=\"mb-8\">\n            <div className=\"text-center mb-8\">\n              <h3 className=\"text-2xl font-bold text-black mb-4\">Crefy Connect SDK</h3>\n              <p className=\"text-black max-w-2xl mx-auto\">\n                Integrate seamless social login with embedded smart wallets.\n                Abstract away Web3 complexity while providing powerful identity infrastructure.\n              </p>\n            </div>\n          </motion.div>\n\n          <motion.div variants={itemVariants}>\n            <Card className=\"bg-gray-900/50 border-gray-700 backdrop-blur-sm\">\n              <CardHeader className=\"flex flex-row items-center justify-between\">\n                <div>\n                  <CardTitle className=\"text-white\">\n                    {activeTab === \"phygital\" ? \"Tokenize Physical Products\" : \"Social Login with Smart Wallets\"}\n                  </CardTitle>\n                  <CardDescription className=\"text-black\">\n                    Implement seamless Web3 onboarding with Connect SDK\n                  </CardDescription>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => copyToClipboard(connectCode, \"connect\")}\n                  className=\"glass-effect border-purple-200 text-black hover:bg-purple-50\"\n                >\n                  {copiedCode === \"connect\" ? (\n                    <Check className=\"w-4 h-4\" />\n                  ) : (\n                    <Copy className=\"w-4 h-4\" />\n                  )}\n                </Button>\n              </CardHeader>\n              <CardContent>\n                <div className=\"glass-effect-dark rounded-lg p-4 overflow-x-auto\">\n                  <pre className=\"text-sm text-black\">\n                    <code>{connectCode}</code>\n                  </pre>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </motion.div>\n\n        {/* Developer Resources */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"mb-16\"\n        >\n          <motion.h3 \n            variants={itemVariants}\n            className=\"text-3xl font-bold text-center mb-12\"\n          >\n            Developer Resources\n          </motion.h3>\n          \n          <motion.div\n            variants={containerVariants}\n            className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\"\n          >\n            {resources.map((resource, index) => (\n              <motion.div key={index} variants={itemVariants}>\n                <Card className=\"h-full bg-gray-900/30 border-gray-700 hover:border-purple-500 transition-all duration-300 hover:bg-gray-900/50 group cursor-pointer\">\n                  <CardHeader>\n                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center mb-4 ${\n                      resource.color === 'purple' \n                        ? 'bg-purple-600/20 group-hover:bg-purple-600/30' \n                        : 'bg-indigo-600/20 group-hover:bg-indigo-600/30'\n                    } transition-colors duration-300`}>\n                      <resource.icon className={`w-6 h-6 ${\n                        resource.color === 'purple' ? 'text-purple-400' : 'text-indigo-400'\n                      }`} />\n                    </div>\n                    <CardTitle className=\"text-white text-lg\">\n                      {resource.title}\n                    </CardTitle>\n                    <CardDescription className=\"text-gray-400\">\n                      {resource.description}\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"flex items-center text-purple-400 group-hover:text-purple-300 transition-colors duration-300\">\n                      <span className=\"text-sm font-medium\">Learn more</span>\n                      <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </motion.div>\n        </motion.div>\n\n        {/* Quick Start */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"text-center\"\n        >\n          <motion.div \n            variants={itemVariants}\n            className=\"bg-gradient-to-r from-purple-600/20 to-indigo-600/20 rounded-3xl p-8 md:p-12 border border-purple-500/30\"\n          >\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              Ready to Start Building?\n            </h3>\n            <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\n              Join thousands of developers building the future of identity infrastructure. \n              Get started with our free tier and scale as you grow.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button \n                size=\"lg\" \n                className=\"bg-gradient-purple hover:opacity-90 text-white px-8 py-4 text-lg font-semibold\"\n              >\n                <Play className=\"mr-2 w-5 h-5\" />\n                Start Free Trial\n              </Button>\n              <Button \n                variant=\"outline\" \n                size=\"lg\"\n                className=\"border-2 border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-8 py-4 text-lg font-semibold\"\n              >\n                <FileText className=\"mr-2 w-5 h-5\" />\n                View Documentation\n              </Button>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAmBO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,kBAAkB,CAAC,MAAc;QACrC,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,cAAc;QACd,WAAW,IAAM,cAAc,OAAO;IACxC;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;uDA0BgC,CAAC;IAEtD,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;8CA2BuB,CAAC;IAE7C,MAAM,YAAY;QAChB;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAa,WAAU;kBACjC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;;gCACX;8CAEC,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAElC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAOH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;sCAC5C,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAE,WAAU;kDAA+B;;;;;;;;;;;;;;;;;sCAOhD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;sCACpB,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC;;kEACC,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,cAAc,aAAa,+BAA+B;;;;;;kEAE7D,6LAAC,mIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAa;;;;;;;;;;;;0DAI1C,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,gBAAgB,aAAa;gDAC5C,WAAU;0DAET,eAAe,0BACd,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;yEAEjB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAItB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;8DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAET,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAAa,UAAU;8CAChC,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC;wDAAI,WAAW,CAAC,2DAA2D,EAC1E,SAAS,KAAK,KAAK,WACf,kDACA,gDACL,+BAA+B,CAAC;kEAC/B,cAAA,6LAAC,SAAS,IAAI;4DAAC,WAAW,CAAC,QAAQ,EACjC,SAAS,KAAK,KAAK,WAAW,oBAAoB,mBAClD;;;;;;;;;;;kEAEJ,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,SAAS,KAAK;;;;;;kEAEjB,6LAAC,mIAAA,CAAA,kBAAe;wDAAC,WAAU;kEACxB,SAAS,WAAW;;;;;;;;;;;;0DAGzB,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCAtBb;;;;;;;;;;;;;;;;8BAgCvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;8BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;GAjSgB;KAAA", "debugId": null}}, {"offset": {"line": 2667, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/contact.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  Mail,\n  MessageSquare,\n  Calendar,\n  Users,\n  ArrowRight,\n  CheckCircle,\n  Building,\n  Code,\n  Zap\n} from \"lucide-react\"\n\nexport function ContactSection() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    company: \"\",\n    role: \"\",\n    message: \"\",\n    interest: \"general\"\n  })\n  const [isSubmitted, setIsSubmitted] = useState(false)\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Handle form submission here\n    setIsSubmitted(true)\n    setTimeout(() => setIsSubmitted(false), 3000)\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  }\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  }\n\n  const contactOptions = [\n    {\n      icon: Calendar,\n      title: \"Schedule a Demo\",\n      description: \"Book a personalized demo to see CREFY in action\",\n      action: \"Book Demo\",\n      color: \"purple\"\n    },\n    {\n      icon: MessageSquare,\n      title: \"Technical Support\",\n      description: \"Get help with integration and technical questions\",\n      action: \"Contact Support\",\n      color: \"indigo\"\n    },\n    {\n      icon: Users,\n      title: \"Partnership Inquiry\",\n      description: \"Explore partnership opportunities and collaborations\",\n      action: \"Partner with Us\",\n      color: \"purple\"\n    }\n  ]\n\n  return (\n    <section id=\"contact\" className=\"relative py-24 bg-white overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-black mb-6\"\n          >\n            Ready to Get\n            <br />\n            <span className=\"text-gradient\">Started?</span>\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-black max-w-3xl mx-auto\"\n          >\n            Join the future of identity infrastructure. Whether you're a developer, enterprise, \n            or partner, we're here to help you succeed.\n          </motion.p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\n          {/* Contact Form */}\n          <motion.div\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true }}\n            variants={itemVariants}\n          >\n            <Card className=\"glass-effect border-2 border-purple-100 hover-lift scroll-reveal\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-bold text-black flex items-center\">\n                  <Mail className=\"w-6 h-6 mr-3 text-purple-600\" />\n                  Get in Touch\n                </CardTitle>\n                <CardDescription className=\"text-black\">\n                  Tell us about your project and we'll get back to you within 24 hours.\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                {isSubmitted ? (\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.8 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className=\"text-center py-8\"\n                  >\n                    <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                      Message Sent Successfully!\n                    </h3>\n                    <p className=\"text-gray-600\">\n                      We'll get back to you within 24 hours.\n                    </p>\n                  </motion.div>\n                ) : (\n                  <form onSubmit={handleSubmit} className=\"space-y-6\">\n                    <div className=\"grid md:grid-cols-2 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Full Name *\n                        </label>\n                        <input\n                          type=\"text\"\n                          name=\"name\"\n                          required\n                          value={formData.name}\n                          onChange={handleInputChange}\n                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                          placeholder=\"John Doe\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Email Address *\n                        </label>\n                        <input\n                          type=\"email\"\n                          name=\"email\"\n                          required\n                          value={formData.email}\n                          onChange={handleInputChange}\n                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                          placeholder=\"<EMAIL>\"\n                        />\n                      </div>\n                    </div>\n\n                    <div className=\"grid md:grid-cols-2 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Company\n                        </label>\n                        <input\n                          type=\"text\"\n                          name=\"company\"\n                          value={formData.company}\n                          onChange={handleInputChange}\n                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                          placeholder=\"Your Company\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Role\n                        </label>\n                        <select\n                          name=\"role\"\n                          value={formData.role}\n                          onChange={handleInputChange}\n                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                        >\n                          <option value=\"\">Select Role</option>\n                          <option value=\"developer\">Developer</option>\n                          <option value=\"cto\">CTO</option>\n                          <option value=\"product-manager\">Product Manager</option>\n                          <option value=\"founder\">Founder</option>\n                          <option value=\"other\">Other</option>\n                        </select>\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Interest\n                      </label>\n                      <select\n                        name=\"interest\"\n                        value={formData.interest}\n                        onChange={handleInputChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                      >\n                        <option value=\"general\">General Inquiry</option>\n                        <option value=\"phygital\">Crefy Phygital</option>\n                        <option value=\"connect\">Crefy Connect</option>\n                        <option value=\"enterprise\">Enterprise Solutions</option>\n                        <option value=\"partnership\">Partnership</option>\n                      </select>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Message *\n                      </label>\n                      <textarea\n                        name=\"message\"\n                        required\n                        rows={4}\n                        value={formData.message}\n                        onChange={handleInputChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none\"\n                        placeholder=\"Tell us about your project and how we can help...\"\n                      />\n                    </div>\n\n                    <Button \n                      type=\"submit\"\n                      className=\"w-full bg-gradient-purple hover:opacity-90 text-white py-3 text-lg font-semibold\"\n                    >\n                      Send Message\n                      <ArrowRight className=\"ml-2 w-5 h-5\" />\n                    </Button>\n                  </form>\n                )}\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Contact Options */}\n          <motion.div\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true }}\n            variants={containerVariants}\n            className=\"space-y-6\"\n          >\n            {contactOptions.map((option, index) => (\n              <motion.div key={index} variants={itemVariants}>\n                <Card className=\"border-2 border-gray-100 hover:border-purple-200 transition-all duration-300 hover:shadow-lg group cursor-pointer\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-start space-x-4\">\n                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${\n                        option.color === 'purple' \n                          ? 'bg-purple-100 group-hover:bg-purple-200' \n                          : 'bg-indigo-100 group-hover:bg-indigo-200'\n                      } transition-colors duration-300`}>\n                        <option.icon className={`w-6 h-6 ${\n                          option.color === 'purple' ? 'text-purple-600' : 'text-indigo-600'\n                        }`} />\n                      </div>\n                      <div className=\"flex-1\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                          {option.title}\n                        </h3>\n                        <p className=\"text-gray-600 mb-4\">\n                          {option.description}\n                        </p>\n                        <div className=\"flex items-center text-purple-600 group-hover:text-purple-700 transition-colors duration-300\">\n                          <span className=\"font-medium\">{option.action}</span>\n                          <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n\n            {/* Quick Stats */}\n            <motion.div variants={itemVariants}>\n              <Card className=\"bg-gradient-to-br from-purple-600 to-indigo-600 text-white border-0\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold mb-4\">Why Choose CREFY?</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <Building className=\"w-5 h-5\" />\n                      <span>Trusted by 50+ companies</span>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <Code className=\"w-5 h-5\" />\n                      <span>Developer-first approach</span>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <Zap className=\"w-5 h-5\" />\n                      <span>99.9% uptime SLA</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAkBO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,SAAS;QACT,UAAU;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,eAAe;QACf,WAAW,IAAM,eAAe,QAAQ;IAC1C;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,iBAAiB;QACrB;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;YACb,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,QAAQ;YACR,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;;sCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;;gCACX;8CAEC,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAElC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAMH,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;4BAAK;4BACvB,UAAU;sCAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;0DAGnD,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAa;;;;;;;;;;;;kDAI1C,6LAAC,mIAAA,CAAA,cAAW;kDACT,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,WAAU;;8DAEV,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DAGzD,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;iEAK/B,6LAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,QAAQ;oEACR,OAAO,SAAS,IAAI;oEACpB,UAAU;oEACV,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAGhB,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,QAAQ;oEACR,OAAO,SAAS,KAAK;oEACrB,UAAU;oEACV,WAAU;oEACV,aAAY;;;;;;;;;;;;;;;;;;8DAKlB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,SAAS,OAAO;oEACvB,UAAU;oEACV,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAGhB,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,SAAS,IAAI;oEACpB,UAAU;oEACV,WAAU;;sFAEV,6LAAC;4EAAO,OAAM;sFAAG;;;;;;sFACjB,6LAAC;4EAAO,OAAM;sFAAY;;;;;;sFAC1B,6LAAC;4EAAO,OAAM;sFAAM;;;;;;sFACpB,6LAAC;4EAAO,OAAM;sFAAkB;;;;;;sFAChC,6LAAC;4EAAO,OAAM;sFAAU;;;;;;sFACxB,6LAAC;4EAAO,OAAM;sFAAQ;;;;;;;;;;;;;;;;;;;;;;;;8DAK5B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,QAAQ;4DACxB,UAAU;4DACV,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,6LAAC;oEAAO,OAAM;8EAAW;;;;;;8EACzB,6LAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,6LAAC;oEAAO,OAAM;8EAAa;;;;;;8EAC3B,6LAAC;oEAAO,OAAM;8EAAc;;;;;;;;;;;;;;;;;;8DAIhC,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,QAAQ;4DACR,MAAM;4DACN,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;;wDACX;sEAEC,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;4BAAK;4BACvB,UAAU;4BACV,WAAU;;gCAET,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAAa,UAAU;kDAChC,cAAA,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,sDAAsD,EACrE,OAAO,KAAK,KAAK,WACb,4CACA,0CACL,+BAA+B,CAAC;sEAC/B,cAAA,6LAAC,OAAO,IAAI;gEAAC,WAAW,CAAC,QAAQ,EAC/B,OAAO,KAAK,KAAK,WAAW,oBAAoB,mBAChD;;;;;;;;;;;sEAEJ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EACX,OAAO,KAAK;;;;;;8EAEf,6LAAC;oEAAE,WAAU;8EACV,OAAO,WAAW;;;;;;8EAErB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAe,OAAO,MAAM;;;;;;sFAC5C,6LAAC,qNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAtBjB;;;;;8CAgCnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;8CACpB,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5B;GArTgB;KAAA", "debugId": null}}, {"offset": {"line": 3471, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/sections/footer.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { \n  Github, \n  Twitter, \n  Linkedin, \n  Mail, \n  ArrowRight,\n  Code,\n  FileText,\n  Users,\n  Building\n} from \"lucide-react\"\n\nexport function Footer() {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  }\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  }\n\n  const footerLinks = {\n    products: [\n      { name: \"Crefy Phygital\", href: \"#products\" },\n      { name: \"Crefy Connect\", href: \"#products\" },\n      { name: \"Enterprise\", href: \"#contact\" },\n      { name: \"Pricing\", href: \"#pricing\" }\n    ],\n    developers: [\n      { name: \"Documentation\", href: \"/docs\" },\n      { name: \"API Reference\", href: \"/docs/api\" },\n      { name: \"SDKs\", href: \"/docs/sdks\" },\n      { name: \"Examples\", href: \"/examples\" }\n    ],\n    company: [\n      { name: \"About\", href: \"/about\" },\n      { name: \"Blog\", href: \"/blog\" },\n      { name: \"Careers\", href: \"/careers\" },\n      { name: \"Contact\", href: \"#contact\" }\n    ],\n    legal: [\n      { name: \"Privacy Policy\", href: \"/privacy\" },\n      { name: \"Terms of Service\", href: \"/terms\" },\n      { name: \"Security\", href: \"/security\" },\n      { name: \"Status\", href: \"/status\" }\n    ]\n  }\n\n  const socialLinks = [\n    { icon: Twitter, href: \"https://twitter.com/crefy\", label: \"Twitter\" },\n    { icon: Github, href: \"https://github.com/crefy\", label: \"GitHub\" },\n    { icon: Linkedin, href: \"https://linkedin.com/company/crefy\", label: \"LinkedIn\" },\n    { icon: Mail, href: \"mailto:<EMAIL>\", label: \"Email\" }\n  ]\n\n  return (\n    <footer className=\"bg-white mx-4 mb-4 rounded-2xl glass-effect border border-purple-100/50 text-gray-900\">\n      {/* Newsletter Section */}\n      <motion.div\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true }}\n        variants={containerVariants}\n        className=\"border-b border-purple-100\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <motion.div \n            variants={itemVariants}\n            className=\"text-center max-w-2xl mx-auto\"\n          >\n            <h3 className=\"text-3xl font-bold mb-4\">\n              Stay Updated with CREFY\n            </h3>\n            <p className=\"text-gray-600 mb-8\">\n              Get the latest updates on new features, developer resources, and industry insights.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n              />\n              <Button className=\"bg-gradient-purple hover:opacity-90 text-white px-6\">\n                Subscribe\n                <ArrowRight className=\"ml-2 w-4 h-4\" />\n              </Button>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n\n      {/* Main Footer */}\n      <motion.div\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true }}\n        variants={containerVariants}\n        className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\"\n      >\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <motion.div variants={itemVariants} className=\"lg:col-span-2\">\n            <div className=\"text-3xl font-bold text-gradient mb-4\">CREFY</div>\n            <p className=\"text-gray-400 mb-6 max-w-sm\">\n              Foundational identity infrastructure for the Internet of Value. \n              Powering next-gen interactions between people, products, and platforms.\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social, index) => (\n                <motion.a\n                  key={index}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-purple-600 transition-colors duration-300\"\n                  aria-label={social.label}\n                >\n                  <social.icon className=\"w-5 h-5\" />\n                </motion.a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Products */}\n          <motion.div variants={itemVariants}>\n            <h4 className=\"font-semibold text-white mb-4 flex items-center\">\n              <Code className=\"w-4 h-4 mr-2\" />\n              Products\n            </h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.products.map((link, index) => (\n                <li key={index}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Developers */}\n          <motion.div variants={itemVariants}>\n            <h4 className=\"font-semibold text-white mb-4 flex items-center\">\n              <FileText className=\"w-4 h-4 mr-2\" />\n              Developers\n            </h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.developers.map((link, index) => (\n                <li key={index}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Company */}\n          <motion.div variants={itemVariants}>\n            <h4 className=\"font-semibold text-white mb-4 flex items-center\">\n              <Building className=\"w-4 h-4 mr-2\" />\n              Company\n            </h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link, index) => (\n                <li key={index}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Legal */}\n          <motion.div variants={itemVariants}>\n            <h4 className=\"font-semibold text-white mb-4 flex items-center\">\n              <Users className=\"w-4 h-4 mr-2\" />\n              Legal\n            </h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.legal.map((link, index) => (\n                <li key={index}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n        </div>\n      </motion.div>\n\n      {/* Bottom Bar */}\n      <motion.div\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true }}\n        variants={itemVariants}\n        className=\"border-t border-gray-800\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-400 text-sm\">\n              © 2025 CREFY. All rights reserved.\n            </p>\n            <div className=\"flex items-center space-x-6 mt-4 md:mt-0\">\n              <span className=\"text-gray-400 text-sm\">Built with ❤️ for developers</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-gray-400 text-sm\">All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAgBO,SAAS;IACd,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,cAAc;QAClB,UAAU;YACR;gBAAE,MAAM;gBAAkB,MAAM;YAAY;YAC5C;gBAAE,MAAM;gBAAiB,MAAM;YAAY;YAC3C;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,YAAY;YACV;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAiB,MAAM;YAAY;YAC3C;gBAAE,MAAM;gBAAQ,MAAM;YAAa;YACnC;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,2MAAA,CAAA,UAAO;YAAE,MAAM;YAA6B,OAAO;QAAU;QACrE;YAAE,MAAM,yMAAA,CAAA,SAAM;YAAE,MAAM;YAA4B,OAAO;QAAS;QAClE;YAAE,MAAM,6MAAA,CAAA,WAAQ;YAAE,MAAM;YAAsC,OAAO;QAAW;QAChF;YAAE,MAAM,qMAAA,CAAA,OAAI;YAAE,MAAM;YAA0B,OAAO;QAAQ;KAC9D;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;gBAAK;gBACvB,UAAU;gBACV,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;4CAAsD;0DAEtE,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;gBAAK;gBACvB,UAAU;gBACV,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,6LAAC;oCAAI,WAAU;8CAAwC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CAEP,MAAM,OAAO,IAAI;4CACjB,QAAO;4CACP,KAAI;4CACJ,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;4CACV,cAAY,OAAO,KAAK;sDAExB,cAAA,6LAAC,OAAO,IAAI;gDAAC,WAAU;;;;;;2CATlB;;;;;;;;;;;;;;;;sCAgBb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;;8CACpB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,6LAAC;oCAAG,WAAU;8CACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;sCAaf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;;8CACpB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,6LAAC;oCAAG,WAAU;8CACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;sCAaf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;;8CACpB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;sCAaf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;;8CACpB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,6LAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAenB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;gBAAK;gBACvB,UAAU;gBACV,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxD;KAvOgB", "debugId": null}}, {"offset": {"line": 4064, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/components/ui/global-floating-orbs.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from \"react\"\nimport { motion } from \"framer-motion\"\nimport { useScrollAnimations } from \"@/hooks/use-scroll-animations\"\n\ninterface GlobalOrb {\n  id: number\n  size: number\n  x: number\n  y: number\n  color: string\n  animationClass: string\n  opacity: number\n  delay: number\n  parallaxSpeed: 'slow' | 'medium' | 'fast'\n  shape: 'circle' | 'ellipse' | 'blob'\n  sizeCategory: 'small' | 'medium' | 'large' | 'extra-large'\n}\n\ninterface GlobalFloatingOrbsProps {\n  count?: number\n}\n\nexport function GlobalFloatingOrbs({ count = 20 }: GlobalFloatingOrbsProps) {\n  const { parallaxSlow, parallaxMedium, parallaxFast } = useScrollAnimations()\n  const [orbs, setOrbs] = useState<GlobalOrb[]>([])\n\n  useEffect(() => {\n    const colors = [\n      'rgba(107, 33, 168, 0.15)',   // deep-purple\n      'rgba(67, 56, 202, 0.15)',    // indigo\n      'rgba(124, 58, 237, 0.15)',   // royal-purple\n      'rgba(107, 33, 168, 0.08)',   // deep-purple lighter\n      'rgba(67, 56, 202, 0.08)',    // indigo lighter\n      'rgba(124, 58, 237, 0.08)',   // royal-purple lighter\n    ]\n\n    const animationClasses = [\n      'floating-orb-1',\n      'floating-orb-2', \n      'floating-orb-3',\n      'floating-orb-4',\n      'floating-orb-5'\n    ]\n\n    const parallaxSpeeds: ('slow' | 'medium' | 'fast')[] = ['slow', 'medium', 'fast']\n    const shapes: ('circle' | 'ellipse' | 'blob')[] = ['circle', 'ellipse', 'blob']\n    const sizeCategories: ('small' | 'medium' | 'large' | 'extra-large')[] = ['small', 'medium', 'large', 'extra-large']\n\n    const getSizeFromCategory = (category: 'small' | 'medium' | 'large' | 'extra-large') => {\n      switch (category) {\n        case 'small': return Math.random() * 60 + 30 // 30-90px\n        case 'medium': return Math.random() * 100 + 80 // 80-180px\n        case 'large': return Math.random() * 150 + 150 // 150-300px\n        case 'extra-large': return Math.random() * 200 + 250 // 250-450px\n        default: return Math.random() * 100 + 80\n      }\n    }\n\n    const generateOrbs = () => {\n      const newOrbs: GlobalOrb[] = []\n      \n      for (let i = 0; i < count; i++) {\n        const sizeCategory = sizeCategories[Math.floor(Math.random() * sizeCategories.length)]\n        newOrbs.push({\n          id: i,\n          size: getSizeFromCategory(sizeCategory),\n          x: Math.random() * 120 - 10, // -10% to 110% for edge overflow\n          y: Math.random() * 500, // Spread across entire page height\n          color: colors[Math.floor(Math.random() * colors.length)],\n          animationClass: animationClasses[Math.floor(Math.random() * animationClasses.length)],\n          opacity: Math.random() * 0.25 + 0.05, // 0.05 to 0.3\n          delay: Math.random() * 10, // 0 to 10 seconds delay\n          parallaxSpeed: parallaxSpeeds[Math.floor(Math.random() * parallaxSpeeds.length)],\n          shape: shapes[Math.floor(Math.random() * shapes.length)],\n          sizeCategory,\n        })\n      }\n      \n      setOrbs(newOrbs)\n    }\n\n    generateOrbs()\n  }, [count])\n\n  const getParallaxTransform = (speed: 'slow' | 'medium' | 'fast') => {\n    switch (speed) {\n      case 'slow': return parallaxSlow\n      case 'medium': return parallaxMedium\n      case 'fast': return parallaxFast\n      default: return parallaxSlow\n    }\n  }\n\n  const getShapeStyles = (shape: 'circle' | 'ellipse' | 'blob', size: number) => {\n    const baseStyles = {\n      width: `${size}px`,\n      height: `${size}px`,\n    }\n\n    switch (shape) {\n      case 'circle':\n        return { ...baseStyles, borderRadius: '50%' }\n      case 'ellipse':\n        return { \n          ...baseStyles, \n          width: `${size * 1.4}px`,\n          height: `${size * 0.7}px`,\n          borderRadius: '50%' \n        }\n      case 'blob':\n        return { \n          ...baseStyles, \n          borderRadius: `${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}% ${Math.random() * 30 + 40}%` \n        }\n      default:\n        return { ...baseStyles, borderRadius: '50%' }\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 overflow-hidden pointer-events-none z-0\">\n      {orbs.map((orb) => (\n        <motion.div\n          key={orb.id}\n          className={`floating-orb ${orb.animationClass}`}\n          style={{\n            ...getShapeStyles(orb.shape, orb.size),\n            left: `${orb.x}%`,\n            top: `${orb.y}vh`,\n            backgroundColor: orb.color,\n            opacity: orb.opacity,\n            animationDelay: `${orb.delay}s`,\n            transform: `translate(-50%, -50%)`,\n            y: getParallaxTransform(orb.parallaxSpeed),\n          }}\n        />\n      ))}\n      \n      {/* Additional gradient overlays for depth */}\n      <motion.div \n        className=\"absolute inset-0 bg-gradient-to-br from-transparent via-white/2 to-transparent pointer-events-none\"\n        style={{ y: parallaxSlow }}\n      />\n      <motion.div \n        className=\"absolute inset-0 bg-gradient-to-tr from-purple-50/10 via-transparent to-indigo-50/10 pointer-events-none\"\n        style={{ y: parallaxMedium }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAwBO,SAAS,mBAAmB,EAAE,QAAQ,EAAE,EAA2B;;IACxE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;IACzE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,mBAAmB;gBACvB;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,iBAAiD;gBAAC;gBAAQ;gBAAU;aAAO;YACjF,MAAM,SAA4C;gBAAC;gBAAU;gBAAW;aAAO;YAC/E,MAAM,iBAAmE;gBAAC;gBAAS;gBAAU;gBAAS;aAAc;YAEpH,MAAM;oEAAsB,CAAC;oBAC3B,OAAQ;wBACN,KAAK;4BAAS,OAAO,KAAK,MAAM,KAAK,KAAK,GAAG,UAAU;;wBACvD,KAAK;4BAAU,OAAO,KAAK,MAAM,KAAK,MAAM,GAAG,WAAW;;wBAC1D,KAAK;4BAAS,OAAO,KAAK,MAAM,KAAK,MAAM,IAAI,YAAY;;wBAC3D,KAAK;4BAAe,OAAO,KAAK,MAAM,KAAK,MAAM,IAAI,YAAY;;wBACjE;4BAAS,OAAO,KAAK,MAAM,KAAK,MAAM;oBACxC;gBACF;;YAEA,MAAM;6DAAe;oBACnB,MAAM,UAAuB,EAAE;oBAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;wBAC9B,MAAM,eAAe,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;wBACtF,QAAQ,IAAI,CAAC;4BACX,IAAI;4BACJ,MAAM,oBAAoB;4BAC1B,GAAG,KAAK,MAAM,KAAK,MAAM;4BACzB,GAAG,KAAK,MAAM,KAAK;4BACnB,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;4BACxD,gBAAgB,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;4BACrF,SAAS,KAAK,MAAM,KAAK,OAAO;4BAChC,OAAO,KAAK,MAAM,KAAK;4BACvB,eAAe,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;4BAChF,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;4BACxD;wBACF;oBACF;oBAEA,QAAQ;gBACV;;YAEA;QACF;uCAAG;QAAC;KAAM;IAEV,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC,OAAsC;QAC5D,MAAM,aAAa;YACjB,OAAO,GAAG,KAAK,EAAE,CAAC;YAClB,QAAQ,GAAG,KAAK,EAAE,CAAC;QACrB;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,GAAG,UAAU;oBAAE,cAAc;gBAAM;YAC9C,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;oBACxB,QAAQ,GAAG,OAAO,IAAI,EAAE,CAAC;oBACzB,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,cAAc,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,EAAE,EAAE,KAAK,MAAM,KAAK,KAAK,GAAG,EAAE,EAAE,KAAK,MAAM,KAAK,KAAK,GAAG,EAAE,EAAE,KAAK,MAAM,KAAK,KAAK,GAAG,CAAC,CAAC;gBACjI;YACF;gBACE,OAAO;oBAAE,GAAG,UAAU;oBAAE,cAAc;gBAAM;QAChD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAW,CAAC,aAAa,EAAE,IAAI,cAAc,EAAE;oBAC/C,OAAO;wBACL,GAAG,eAAe,IAAI,KAAK,EAAE,IAAI,IAAI,CAAC;wBACtC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBACjB,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC;wBACjB,iBAAiB,IAAI,KAAK;wBAC1B,SAAS,IAAI,OAAO;wBACpB,gBAAgB,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC;wBAC/B,WAAW,CAAC,qBAAqB,CAAC;wBAClC,GAAG,qBAAqB,IAAI,aAAa;oBAC3C;mBAXK,IAAI,EAAE;;;;;0BAgBf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,GAAG;gBAAa;;;;;;0BAE3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,GAAG;gBAAe;;;;;;;;;;;;AAInC;GA/HgB;;QACyC,8IAAA,CAAA,sBAAmB;;;KAD5D", "debugId": null}}, {"offset": {"line": 4267, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/crefy/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Navbar } from \"@/components/navigation/navbar\"\nimport { HeroSection } from \"@/components/sections/hero\"\nimport { ProductsSection } from \"@/components/sections/products\"\nimport { FeaturesSection } from \"@/components/sections/features\"\nimport { DevelopersSection } from \"@/components/sections/developers\"\nimport { ContactSection } from \"@/components/sections/contact\"\nimport { Footer } from \"@/components/sections/footer\"\nimport { GlobalFloatingOrbs } from \"@/components/ui/global-floating-orbs\"\nimport { useScrollReveal } from \"@/hooks/use-scroll-animations\"\n\nexport default function Home() {\n  const scrollRef = useScrollReveal()\n\n  return (\n    <div ref={scrollRef} className=\"min-h-screen bg-white relative\">\n      <GlobalFloatingOrbs count={25} />\n      <Navbar />\n      <main className=\"relative z-10\">\n        <HeroSection />\n        <ProductsSection />\n        <FeaturesSection />\n        <DevelopersSection />\n        <ContactSection />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,YAAY,CAAA,GAAA,8IAAA,CAAA,kBAAe,AAAD;IAEhC,qBACE,6LAAC;QAAI,KAAK;QAAW,WAAU;;0BAC7B,6LAAC,yJAAA,CAAA,qBAAkB;gBAAC,OAAO;;;;;;0BAC3B,6LAAC,6IAAA,CAAA,SAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,yIAAA,CAAA,cAAW;;;;;kCACZ,6LAAC,6IAAA,CAAA,kBAAe;;;;;kCAChB,6LAAC,6IAAA,CAAA,kBAAe;;;;;kCAChB,6LAAC,+IAAA,CAAA,oBAAiB;;;;;kCAClB,6LAAC,4IAAA,CAAA,iBAAc;;;;;;;;;;;0BAEjB,6LAAC,2IAAA,CAAA,SAAM;;;;;;;;;;;AAGb;GAjBwB;;QACJ,8IAAA,CAAA,kBAAe;;;KADX", "debugId": null}}]}